# Dependencies
node_modules/
bun.lockb
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist/
build/
out/
.next/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Electron
app/dist/
release/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# KodeKilat Studio specific
extensions/
user-data/
workspace-cache/
.kodekilat/

# AI models cache
ai-models/
model-cache/

# Extension development
*.vsix
*.kodix

# User settings
user-settings.json
workspace-settings.json
