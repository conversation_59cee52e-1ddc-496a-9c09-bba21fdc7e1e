# Dependencies
node_modules/
bun.lockb
package-lock.json
yarn.lock

# Build outputs
dist/
build/
out/
.next/
coverage/

# Generated files
*.min.js
*.bundle.js
*.map

# Logs
*.log
logs/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Documentation
CHANGELOG.md
LICENSE

# Config files that should not be formatted
.gitignore
.eslintignore
.prettierignore
