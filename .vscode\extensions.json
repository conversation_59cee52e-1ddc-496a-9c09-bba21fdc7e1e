{
  "recommendations": [
    // Essential TypeScript & JavaScript
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    
    // React & Next.js
    "ms-vscode.vscode-react-refactor",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    
    // Git & Version Control
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    "github.copilot",
    
    // Electron Development
    "ms-vscode.vscode-electron",
    
    // Database & SQLite
    "alexcvzz.vscode-sqlite",
    "qwtel.sqlite-viewer",
    
    // Markdown & Documentation
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    
    // Utilities
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.hexeditor",
    "gruntfuggly.todo-tree",
    "aaron-bond.better-comments",
    
    // Theme & Icons
    "pkief.material-icon-theme",
    "github.github-vscode-theme",
    
    // Testing
    "orta.vscode-jest",
    
    // Performance & Monitoring
    "ms-vscode.vscode-performance",
    "waderyan.nodejs-extension-pack"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify",
    "ms-vscode.vscode-css-peek"
  ]
}
