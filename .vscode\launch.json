{"version": "0.2.0", "configurations": [{"name": "Debug Electron Main Process", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/apps/studio", "program": "${workspaceFolder}/apps/studio/main.ts", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "runtimeArgs": ["--inspect=5858", "."], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/studio/dist/**/*.js"]}, {"name": "Debug API Server", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/apps/api-server", "program": "${workspaceFolder}/apps/api-server/index.ts", "runtimeExecutable": "bun", "runtimeArgs": ["--inspect"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "sourceMaps": true}, {"name": "Debug Next.js UI", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/apps/studio/ui", "program": "${workspaceFolder}/apps/studio/ui/node_modules/.bin/next", "args": ["dev"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "sourceMaps": true}, {"name": "Attach to Electron Renderer", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/apps/studio/ui", "sourceMaps": true}], "compounds": [{"name": "Debug Full Stack", "configurations": ["Debug API Server", "Debug Next.js UI", "Debug Electron Main Process"]}]}