{"version": "2.0.0", "tasks": [{"label": "Start All Development Servers", "type": "shell", "command": "bun", "args": ["run", "dev"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "<PERSON> (Legacy)", "type": "shell", "command": "bun run apps/studio/dev.ts", "group": "build"}, {"label": "Start API Server", "type": "shell", "command": "bun", "args": ["run", "dev:api"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start UI Development", "type": "shell", "command": "bun", "args": ["run", "dev:ui"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start Electron", "type": "shell", "command": "bun", "args": ["run", "dev:electron"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🔧 Build All Packages", "type": "shell", "command": "bun", "args": ["run", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "🧹 Clean All", "type": "shell", "command": "bun", "args": ["run", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🔍 Lint All", "type": "shell", "command": "bun", "args": ["run", "lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "✨ Format All", "type": "shell", "command": "bun", "args": ["run", "format"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🧪 Run Tests", "type": "shell", "command": "bun", "args": ["test"], "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🏗️ Build for Production", "type": "shell", "command": "bun", "args": ["run", "build:prod"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}]}