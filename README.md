# KodeKilat Studio ⚡

**IDE Nusantara Modern, <PERSON><PERSON>, <PERSON>, dan <PERSON>-<PERSON> — 100% Offline**

> "IDE Nusantara Lokal. Real-Time. Cerdas. Tanpa Dummy."

## 🚀 Fitur Utama

- 🎨 **UI/UX Modern**: VSCode Redesign dengan tema Nusantara
- 🤖 **KodeKilat AI**: AI Assistant menggantikan GitHub Copilot
- 🧩 **Dual Extension Support**: `.vsix` (VSCode) + `.kodix` (lokal open-source)
- 🎯 **UI Designer**: Visual Editor + Live Preview seperti Onlook IDE
- 📡 **Remote Collaboration**: Live Share & Real-time coding
- ⚡ **Performance**: Native Electron + Bun backend
- 🌐 **100% Offline**: Tidak ada dummy, semua fitur real

## 🏗️ Arsitektur Monorepo

```
kodekilat-studio/
├── apps/
│   ├── studio/                 # Electron Main Process
│   ├── studio/ui/             # Next.js Frontend
│   └── api-server/            # Bun Backend API
├── packages/                  # Shared Components
├── shared/                    # Common Utilities
└── .vscode/                   # Development Config
```

## 🛠️ Tech Stack

- **Frontend**: Next.js + TailwindCSS + shadcn/ui + Framer Motion
- **Backend**: Bun + SQLite
- **Desktop**: Electron
- **AI**: OpenRouter, HuggingFace, Ollama
- **Collaboration**: WebRTC + Socket.io

## 🚀 Quick Start

```bash
# Install dependencies
bun install

# Start development
bun run dev

# Build for production
bun run build
```

## 📦 Extensions

- **VSCode Compatible**: Install `.vsix` extensions
- **KodeKilat Native**: Create `.kodix` extensions
- **Marketplace**: Browse and install extensions

## 🤝 Contributing

Kontribusi terbuka untuk komunitas developer Nusantara!

## 📄 License

MIT License - Open Source untuk semua

---

**Dibuat dengan ❤️ untuk komunitas developer Indonesia**
