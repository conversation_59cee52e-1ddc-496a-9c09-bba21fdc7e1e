import { serve } from "bun";
import { cors } from "./middleware/cors";
import { router } from "./routes";
import { initDatabase } from "./services/db.service";
import { WebSocketServer } from "./services/websocket.service";

const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || "localhost";

// Initialize database
await initDatabase();

// Initialize WebSocket server for real-time features
const wsServer = new WebSocketServer();

console.log(`🚀 KodeKilat Studio API Server starting...`);

const server = serve({
  port: PORT,
  hostname: HOST,

  async fetch(req, server) {
    const url = new URL(req.url);

    // Handle CORS
    const corsResponse = cors(req);
    if (corsResponse) return corsResponse;

    // Handle WebSocket upgrade
    if (req.headers.get("upgrade") === "websocket") {
      const success = server.upgrade(req, {
        data: {
          userId: url.searchParams.get("userId") || "anonymous",
          sessionId: url.searchParams.get("sessionId") || "default"
        }
      });

      if (success) {
        return undefined; // WebSocket upgrade successful
      }

      return new Response("WebSocket upgrade failed", { status: 400 });
    }

    // Handle API routes
    try {
      const response = await router(req);
      if (response) return response;

      // 404 for unmatched routes
      return new Response(
        JSON.stringify({
          error: "Not Found",
          message: `Route ${url.pathname} not found`
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("API Error:", error);
      return new Response(
        JSON.stringify({
          error: "Internal Server Error",
          message: error instanceof Error ? error.message : "Unknown error"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  websocket: {
    open(ws) {
      wsServer.handleConnection(ws);
    },

    message(ws, message) {
      wsServer.handleMessage(ws, message);
    },

    close(ws, code, message) {
      wsServer.handleDisconnection(ws, code, message);
    }
  }
});

console.log(`✅ KodeKilat Studio API Server running on http://${HOST}:${PORT}`);
console.log(`📡 WebSocket server ready for real-time collaboration`);
console.log(`🗄️  Database initialized and ready`);
console.log(`🔥 Ready to serve IDE Nusantara!`);

// Graceful shutdown
process.on("SIGINT", () => {
  console.log("\n🛑 Shutting down KodeKilat Studio API Server...");
  server.stop();
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\n🛑 Shutting down KodeKilat Studio API Server...");
  server.stop();
  process.exit(0);
});
