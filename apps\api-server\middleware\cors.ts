export function cors(req: Request): Response | null {
  const origin = req.headers.get("origin");
  const method = req.method;
  
  // Allow requests from localhost and Electron app
  const allowedOrigins = [
    "http://localhost:3000",
    "http://localhost:3001", 
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "app://kodekilat-studio", // Electron app protocol
    "file://" // For local file access
  ];
  
  const corsHeaders = {
    "Access-Control-Allow-Origin": allowedOrigins.includes(origin || "") ? origin! : "http://localhost:3000",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
    "Access-Control-Allow-Credentials": "true",
    "Access-Control-Max-Age": "86400" // 24 hours
  };
  
  // Handle preflight requests
  if (method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  
  // For other requests, we'll add headers in the router
  return null;
}

export function addCorsHeaders(response: Response, req: Request): Response {
  const origin = req.headers.get("origin");
  const allowedOrigins = [
    "http://localhost:3000",
    "http://localhost:3001", 
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "app://kodekilat-studio",
    "file://"
  ];
  
  const headers = new Headers(response.headers);
  headers.set("Access-Control-Allow-Origin", allowedOrigins.includes(origin || "") ? origin! : "http://localhost:3000");
  headers.set("Access-Control-Allow-Credentials", "true");
  
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  });
}
