{"name": "@kodekilat/api-server", "version": "1.0.0", "description": "KodeKilat Studio - Bun API Server", "private": true, "main": "index.ts", "scripts": {"dev": "bun run --watch index.ts", "start": "bun run index.ts", "build": "bun build index.ts --outdir ./dist --target bun", "clean": "<PERSON><PERSON><PERSON> dist", "test": "bun test", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix"}, "dependencies": {"better-sqlite3": "^9.2.2", "cors": "^2.8.5", "ws": "^8.14.2", "chokidar": "^3.5.3", "node-fetch": "^3.3.2", "uuid": "^9.0.1", "mime-types": "^2.1.35", "archiver": "^6.0.1", "unzipper": "^0.10.14", "semver": "^7.5.4"}, "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/cors": "^2.8.17", "@types/ws": "^8.5.10", "@types/uuid": "^9.0.7", "@types/mime-types": "^2.1.4", "@types/archiver": "^6.0.2", "@types/semver": "^7.5.6", "bun-types": "latest", "rimraf": "^5.0.5"}, "engines": {"bun": ">=1.0.0"}}