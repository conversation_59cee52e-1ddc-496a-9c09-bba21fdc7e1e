import { getDatabase } from "../services/db.service";

export async function handleDatabaseRoutes(req: Request): Promise<Response | null> {
  const url = new URL(req.url);
  const pathname = url.pathname;
  const method = req.method;
  
  try {
    const db = getDatabase();
    
    // Get user settings
    if (pathname === "/api/db/settings" && method === "GET") {
      const userId = url.searchParams.get("userId") || "default";
      
      const settings = db.prepare(`
        SELECT key, value FROM user_settings 
        WHERE user_id = ?
      `).all(userId);
      
      const settingsObj = settings.reduce((acc: any, row: any) => {
        acc[row.key] = JSON.parse(row.value);
        return acc;
      }, {});
      
      return new Response(JSON.stringify({ settings: settingsObj }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Update user settings
    if (pathname === "/api/db/settings" && method === "POST") {
      const body = await req.json();
      const { userId = "default", settings } = body;
      
      if (!settings) {
        return new Response(JSON.stringify({ error: "Settings object required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO user_settings (user_id, key, value, updated_at)
        VALUES (?, ?, ?, datetime('now'))
      `);
      
      for (const [key, value] of Object.entries(settings)) {
        stmt.run(userId, key, JSON.stringify(value));
      }
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Get recent projects
    if (pathname === "/api/db/projects/recent" && method === "GET") {
      const userId = url.searchParams.get("userId") || "default";
      const limit = parseInt(url.searchParams.get("limit") || "10");
      
      const projects = db.prepare(`
        SELECT * FROM recent_projects 
        WHERE user_id = ? 
        ORDER BY last_opened DESC 
        LIMIT ?
      `).all(userId, limit);
      
      return new Response(JSON.stringify({ projects }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Add/update recent project
    if (pathname === "/api/db/projects/recent" && method === "POST") {
      const body = await req.json();
      const { userId = "default", projectPath, projectName, projectType = "unknown" } = body;
      
      if (!projectPath) {
        return new Response(JSON.stringify({ error: "Project path required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      db.prepare(`
        INSERT OR REPLACE INTO recent_projects 
        (user_id, project_path, project_name, project_type, last_opened)
        VALUES (?, ?, ?, ?, datetime('now'))
      `).run(userId, projectPath, projectName || projectPath.split('/').pop(), projectType);
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Get file history
    if (pathname === "/api/db/files/history" && method === "GET") {
      const filePath = url.searchParams.get("filePath");
      const limit = parseInt(url.searchParams.get("limit") || "50");
      
      if (!filePath) {
        return new Response(JSON.stringify({ error: "File path required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      const history = db.prepare(`
        SELECT * FROM file_history 
        WHERE file_path = ? 
        ORDER BY created_at DESC 
        LIMIT ?
      `).all(filePath, limit);
      
      return new Response(JSON.stringify({ history }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Save file version to history
    if (pathname === "/api/db/files/history" && method === "POST") {
      const body = await req.json();
      const { filePath, content, userId = "default", changeType = "edit" } = body;
      
      if (!filePath || content === undefined) {
        return new Response(JSON.stringify({ error: "File path and content required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      db.prepare(`
        INSERT INTO file_history (file_path, content, user_id, change_type)
        VALUES (?, ?, ?, ?)
      `).run(filePath, content, userId, changeType);
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Get AI chat history
    if (pathname === "/api/db/ai/history" && method === "GET") {
      const userId = url.searchParams.get("userId") || "default";
      const sessionId = url.searchParams.get("sessionId");
      const limit = parseInt(url.searchParams.get("limit") || "100");
      
      let query = `
        SELECT * FROM ai_chat_history 
        WHERE user_id = ?
      `;
      const params: any[] = [userId];
      
      if (sessionId) {
        query += ` AND session_id = ?`;
        params.push(sessionId);
      }
      
      query += ` ORDER BY created_at DESC LIMIT ?`;
      params.push(limit);
      
      const history = db.prepare(query).all(...params);
      
      return new Response(JSON.stringify({ history }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Save AI chat message
    if (pathname === "/api/db/ai/history" && method === "POST") {
      const body = await req.json();
      const { 
        userId = "default", 
        sessionId, 
        message, 
        response, 
        messageType = "chat",
        context 
      } = body;
      
      if (!message) {
        return new Response(JSON.stringify({ error: "Message required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      db.prepare(`
        INSERT INTO ai_chat_history 
        (user_id, session_id, message, response, message_type, context)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        userId, 
        sessionId || `session-${Date.now()}`, 
        message, 
        response || null, 
        messageType,
        context ? JSON.stringify(context) : null
      );
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Get extension data
    if (pathname === "/api/db/extensions" && method === "GET") {
      const extensions = db.prepare(`
        SELECT * FROM extensions 
        ORDER BY installed_at DESC
      `).all();
      
      return new Response(JSON.stringify({ extensions }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Save extension data
    if (pathname === "/api/db/extensions" && method === "POST") {
      const body = await req.json();
      const { 
        extensionId, 
        name, 
        version, 
        enabled = true, 
        extensionType = "vsix",
        metadata = {} 
      } = body;
      
      if (!extensionId || !name) {
        return new Response(JSON.stringify({ error: "Extension ID and name required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      db.prepare(`
        INSERT OR REPLACE INTO extensions 
        (extension_id, name, version, enabled, extension_type, metadata, installed_at)
        VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
      `).run(extensionId, name, version, enabled ? 1 : 0, extensionType, JSON.stringify(metadata));
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Delete extension data
    if (pathname === "/api/db/extensions" && method === "DELETE") {
      const extensionId = url.searchParams.get("extensionId");
      
      if (!extensionId) {
        return new Response(JSON.stringify({ error: "Extension ID required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      db.prepare(`DELETE FROM extensions WHERE extension_id = ?`).run(extensionId);
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
  } catch (error) {
    console.error("Database API error:", error);
    return new Response(JSON.stringify({ 
      error: "Database operation failed", 
      message: error instanceof Error ? error.message : "Unknown error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
  
  return null;
}
