import { promises as fs } from "fs";
import path from "path";
import { watch } from "chokidar";

// File system watcher for real-time updates
const watchers = new Map<string, any>();

export async function handleFileSystemRoutes(req: Request): Promise<Response | null> {
  const url = new URL(req.url);
  const pathname = url.pathname;
  const method = req.method;
  
  try {
    // List directory contents
    if (pathname === "/api/fs/list" && method === "GET") {
      const dirPath = url.searchParams.get("path") || process.cwd();
      const files = await listDirectory(dirPath);
      
      return new Response(JSON.stringify({ files }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Read file content
    if (pathname === "/api/fs/read" && method === "GET") {
      const filePath = url.searchParams.get("path");
      if (!filePath) {
        return new Response(JSON.stringify({ error: "Path parameter required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      const content = await fs.readFile(filePath, "utf-8");
      return new Response(JSON.stringify({ content }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Write file content
    if (pathname === "/api/fs/write" && method === "POST") {
      const body = await req.json();
      const { path: filePath, content } = body;
      
      if (!filePath || content === undefined) {
        return new Response(JSON.stringify({ error: "Path and content required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      // Ensure directory exists
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, content, "utf-8");
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Create directory
    if (pathname === "/api/fs/mkdir" && method === "POST") {
      const body = await req.json();
      const { path: dirPath } = body;
      
      if (!dirPath) {
        return new Response(JSON.stringify({ error: "Path required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      await fs.mkdir(dirPath, { recursive: true });
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Delete file or directory
    if (pathname === "/api/fs/delete" && method === "DELETE") {
      const filePath = url.searchParams.get("path");
      if (!filePath) {
        return new Response(JSON.stringify({ error: "Path parameter required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      const stats = await fs.stat(filePath);
      if (stats.isDirectory()) {
        await fs.rmdir(filePath, { recursive: true });
      } else {
        await fs.unlink(filePath);
      }
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Rename/move file or directory
    if (pathname === "/api/fs/rename" && method === "POST") {
      const body = await req.json();
      const { oldPath, newPath } = body;
      
      if (!oldPath || !newPath) {
        return new Response(JSON.stringify({ error: "oldPath and newPath required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      await fs.rename(oldPath, newPath);
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Watch directory for changes
    if (pathname === "/api/fs/watch" && method === "POST") {
      const body = await req.json();
      const { path: watchPath, sessionId } = body;
      
      if (!watchPath || !sessionId) {
        return new Response(JSON.stringify({ error: "path and sessionId required" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      
      // Stop existing watcher if any
      if (watchers.has(sessionId)) {
        watchers.get(sessionId).close();
      }
      
      // Start new watcher
      const watcher = watch(watchPath, { 
        ignored: /(^|[\/\\])\../, 
        persistent: true 
      });
      
      watcher.on('change', (filePath) => {
        // Broadcast file change via WebSocket
        // This will be handled by the WebSocket service
        console.log(`File changed: ${filePath}`);
      });
      
      watchers.set(sessionId, watcher);
      
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
  } catch (error) {
    console.error("File system error:", error);
    return new Response(JSON.stringify({ 
      error: "File system operation failed", 
      message: error instanceof Error ? error.message : "Unknown error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
  
  return null;
}

async function listDirectory(dirPath: string) {
  const items = await fs.readdir(dirPath, { withFileTypes: true });
  
  const files = await Promise.all(
    items.map(async (item) => {
      const fullPath = path.join(dirPath, item.name);
      const stats = await fs.stat(fullPath);
      
      return {
        name: item.name,
        path: fullPath,
        type: item.isDirectory() ? "directory" : "file",
        size: stats.size,
        modified: stats.mtime.toISOString(),
        created: stats.birthtime.toISOString()
      };
    })
  );
  
  // Sort: directories first, then files, both alphabetically
  return files.sort((a, b) => {
    if (a.type !== b.type) {
      return a.type === "directory" ? -1 : 1;
    }
    return a.name.localeCompare(b.name);
  });
}
