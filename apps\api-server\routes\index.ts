import { addCorsHeaders } from "../middleware/cors";
import { handleFileSystemRoutes } from "./fs";
import { handleAIRoutes } from "./ai";
import { handleExtensionRoutes } from "./extensions";
import { handlePreviewRoutes } from "./preview";
import { handleRemoteRoutes } from "./remote";
import { handleDatabaseRoutes } from "./db";

export async function router(req: Request): Promise<Response | null> {
  const url = new URL(req.url);
  const pathname = url.pathname;
  
  // Health check
  if (pathname === "/health" || pathname === "/") {
    const response = new Response(
      JSON.stringify({ 
        status: "healthy", 
        service: "KodeKilat Studio API",
        version: "1.0.0",
        timestamp: new Date().toISOString()
      }), 
      { 
        status: 200, 
        headers: { "Content-Type": "application/json" } 
      }
    );
    return addCorsHeaders(response, req);
  }
  
  // API routes
  if (pathname.startsWith("/api/")) {
    let response: Response | null = null;
    
    // File System routes
    if (pathname.startsWith("/api/fs/")) {
      response = await handleFileSystemRoutes(req);
    }
    // AI routes
    else if (pathname.startsWith("/api/ai/")) {
      response = await handleAIRoutes(req);
    }
    // Extension routes
    else if (pathname.startsWith("/api/extensions/")) {
      response = await handleExtensionRoutes(req);
    }
    // Preview routes
    else if (pathname.startsWith("/api/preview/")) {
      response = await handlePreviewRoutes(req);
    }
    // Remote collaboration routes
    else if (pathname.startsWith("/api/remote/")) {
      response = await handleRemoteRoutes(req);
    }
    // Database routes
    else if (pathname.startsWith("/api/db/")) {
      response = await handleDatabaseRoutes(req);
    }
    
    if (response) {
      return addCorsHeaders(response, req);
    }
  }
  
  return null;
}
