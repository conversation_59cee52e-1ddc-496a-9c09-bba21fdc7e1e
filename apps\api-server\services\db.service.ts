import Database from "better-sqlite3";
import { promises as fs } from "fs";
import path from "path";

let db: Database.Database | null = null;

export async function initDatabase() {
  try {
    // Create data directory if it doesn't exist
    const dataDir = path.join(process.cwd(), "data");
    await fs.mkdir(dataDir, { recursive: true });
    
    const dbPath = path.join(dataDir, "kodekilat-studio.db");
    
    // Initialize SQLite database
    db = new Database(dbPath);
    
    // Enable WAL mode for better performance
    db.pragma("journal_mode = WAL");
    db.pragma("synchronous = NORMAL");
    db.pragma("cache_size = 1000000");
    db.pragma("temp_store = memory");
    
    // Create tables
    await createTables();
    
    console.log("✅ Database initialized successfully");
    
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    throw error;
  }
}

export function getDatabase(): Database.Database {
  if (!db) {
    throw new Error("Database not initialized. Call initDatabase() first.");
  }
  return db;
}

async function createTables() {
  if (!db) throw new Error("Database not initialized");
  
  // User settings table
  db.exec(`
    CREATE TABLE IF NOT EXISTS user_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      key TEXT NOT NULL,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(user_id, key)
    )
  `);
  
  // Recent projects table
  db.exec(`
    CREATE TABLE IF NOT EXISTS recent_projects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      project_path TEXT NOT NULL,
      project_name TEXT NOT NULL,
      project_type TEXT DEFAULT 'unknown',
      last_opened DATETIME DEFAULT CURRENT_TIMESTAMP,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(user_id, project_path)
    )
  `);
  
  // File history table for version control
  db.exec(`
    CREATE TABLE IF NOT EXISTS file_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      file_path TEXT NOT NULL,
      content TEXT NOT NULL,
      user_id TEXT NOT NULL,
      change_type TEXT DEFAULT 'edit',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // AI chat history table
  db.exec(`
    CREATE TABLE IF NOT EXISTS ai_chat_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      session_id TEXT NOT NULL,
      message TEXT NOT NULL,
      response TEXT,
      message_type TEXT DEFAULT 'chat',
      context TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Extensions table
  db.exec(`
    CREATE TABLE IF NOT EXISTS extensions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      extension_id TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      version TEXT NOT NULL,
      enabled BOOLEAN DEFAULT 1,
      extension_type TEXT DEFAULT 'vsix',
      metadata TEXT,
      installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Collaboration sessions table
  db.exec(`
    CREATE TABLE IF NOT EXISTS collaboration_sessions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      session_id TEXT UNIQUE NOT NULL,
      session_name TEXT NOT NULL,
      project_path TEXT NOT NULL,
      owner_id TEXT NOT NULL,
      is_public BOOLEAN DEFAULT 0,
      session_data TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Session participants table
  db.exec(`
    CREATE TABLE IF NOT EXISTS session_participants (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      session_id TEXT NOT NULL,
      user_id TEXT NOT NULL,
      user_name TEXT NOT NULL,
      role TEXT DEFAULT 'collaborator',
      permissions TEXT,
      joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_active DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(session_id, user_id)
    )
  `);
  
  // Workspace settings table
  db.exec(`
    CREATE TABLE IF NOT EXISTS workspace_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      workspace_path TEXT UNIQUE NOT NULL,
      settings TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Code snippets table
  db.exec(`
    CREATE TABLE IF NOT EXISTS code_snippets (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      language TEXT NOT NULL,
      code TEXT NOT NULL,
      tags TEXT,
      is_public BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
    CREATE INDEX IF NOT EXISTS idx_recent_projects_user_id ON recent_projects(user_id);
    CREATE INDEX IF NOT EXISTS idx_recent_projects_last_opened ON recent_projects(last_opened);
    CREATE INDEX IF NOT EXISTS idx_file_history_file_path ON file_history(file_path);
    CREATE INDEX IF NOT EXISTS idx_file_history_created_at ON file_history(created_at);
    CREATE INDEX IF NOT EXISTS idx_ai_chat_history_user_id ON ai_chat_history(user_id);
    CREATE INDEX IF NOT EXISTS idx_ai_chat_history_session_id ON ai_chat_history(session_id);
    CREATE INDEX IF NOT EXISTS idx_extensions_enabled ON extensions(enabled);
    CREATE INDEX IF NOT EXISTS idx_collaboration_sessions_owner_id ON collaboration_sessions(owner_id);
    CREATE INDEX IF NOT EXISTS idx_session_participants_session_id ON session_participants(session_id);
    CREATE INDEX IF NOT EXISTS idx_workspace_settings_workspace_path ON workspace_settings(workspace_path);
    CREATE INDEX IF NOT EXISTS idx_code_snippets_user_id ON code_snippets(user_id);
    CREATE INDEX IF NOT EXISTS idx_code_snippets_language ON code_snippets(language);
  `);
  
  // Insert default settings if they don't exist
  const defaultSettings = {
    theme: "dark",
    fontSize: 14,
    fontFamily: "Fira Code",
    tabSize: 2,
    wordWrap: true,
    minimap: true,
    lineNumbers: true,
    autoSave: true,
    formatOnSave: true,
    aiProvider: "claude-3.5-sonnet",
    language: "id"
  };
  
  const existingSettings = db.prepare(`
    SELECT COUNT(*) as count FROM user_settings WHERE user_id = 'default'
  `).get() as { count: number };
  
  if (existingSettings.count === 0) {
    const stmt = db.prepare(`
      INSERT INTO user_settings (user_id, key, value)
      VALUES (?, ?, ?)
    `);
    
    for (const [key, value] of Object.entries(defaultSettings)) {
      stmt.run("default", key, JSON.stringify(value));
    }
    
    console.log("✅ Default settings inserted");
  }
  
  console.log("✅ Database tables created/verified");
}

export function closeDatabase() {
  if (db) {
    db.close();
    db = null;
    console.log("✅ Database connection closed");
  }
}

// Utility functions for common database operations
export class DatabaseService {
  static getUserSettings(userId: string = "default") {
    const db = getDatabase();
    const settings = db.prepare(`
      SELECT key, value FROM user_settings WHERE user_id = ?
    `).all(userId);
    
    return settings.reduce((acc: any, row: any) => {
      acc[row.key] = JSON.parse(row.value);
      return acc;
    }, {});
  }
  
  static setUserSetting(userId: string = "default", key: string, value: any) {
    const db = getDatabase();
    db.prepare(`
      INSERT OR REPLACE INTO user_settings (user_id, key, value, updated_at)
      VALUES (?, ?, ?, datetime('now'))
    `).run(userId, key, JSON.stringify(value));
  }
  
  static addRecentProject(userId: string = "default", projectPath: string, projectName?: string, projectType?: string) {
    const db = getDatabase();
    db.prepare(`
      INSERT OR REPLACE INTO recent_projects 
      (user_id, project_path, project_name, project_type, last_opened)
      VALUES (?, ?, ?, ?, datetime('now'))
    `).run(
      userId, 
      projectPath, 
      projectName || projectPath.split('/').pop() || projectPath,
      projectType || "unknown"
    );
  }
  
  static getRecentProjects(userId: string = "default", limit: number = 10) {
    const db = getDatabase();
    return db.prepare(`
      SELECT * FROM recent_projects 
      WHERE user_id = ? 
      ORDER BY last_opened DESC 
      LIMIT ?
    `).all(userId, limit);
  }
  
  static saveFileHistory(filePath: string, content: string, userId: string = "default", changeType: string = "edit") {
    const db = getDatabase();
    db.prepare(`
      INSERT INTO file_history (file_path, content, user_id, change_type)
      VALUES (?, ?, ?, ?)
    `).run(filePath, content, userId, changeType);
  }
  
  static getFileHistory(filePath: string, limit: number = 50) {
    const db = getDatabase();
    return db.prepare(`
      SELECT * FROM file_history 
      WHERE file_path = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `).all(filePath, limit);
  }
}
