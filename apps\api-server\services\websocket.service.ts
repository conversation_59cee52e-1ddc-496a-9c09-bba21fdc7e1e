import { ServerWebSocket } from "bun";

interface WebSocketData {
  userId: string;
  sessionId: string;
}

interface ConnectedClient {
  ws: ServerWebSocket<WebSocketData>;
  userId: string;
  sessionId: string;
  joinedAt: Date;
  lastActivity: Date;
}

export class WebSocketServer {
  private clients = new Map<string, ConnectedClient>();
  private sessions = new Map<string, Set<string>>();
  
  constructor() {
    // Clean up inactive connections every 30 seconds
    setInterval(() => {
      this.cleanupInactiveConnections();
    }, 30000);
  }
  
  handleConnection(ws: ServerWebSocket<WebSocketData>) {
    const { userId, sessionId } = ws.data;
    const clientId = `${userId}-${Date.now()}`;
    
    const client: ConnectedClient = {
      ws,
      userId,
      sessionId,
      joinedAt: new Date(),
      lastActivity: new Date()
    };
    
    this.clients.set(clientId, client);
    
    // Add to session
    if (!this.sessions.has(sessionId)) {
      this.sessions.set(sessionId, new Set());
    }
    this.sessions.get(sessionId)!.add(clientId);
    
    console.log(`🔗 WebSocket connected: ${userId} (${clientId}) in session ${sessionId}`);
    
    // Send welcome message
    this.sendToClient(clientId, {
      type: "connection",
      status: "connected",
      clientId,
      timestamp: new Date().toISOString()
    });
    
    // Notify other clients in the session
    this.broadcastToSession(sessionId, {
      type: "user_joined",
      userId,
      clientId,
      timestamp: new Date().toISOString()
    }, clientId);
    
    // Send current session info
    this.sendSessionInfo(clientId);
  }
  
  handleMessage(ws: ServerWebSocket<WebSocketData>, message: string | Buffer) {
    try {
      const data = JSON.parse(message.toString());
      const { userId, sessionId } = ws.data;
      const clientId = this.findClientId(ws);
      
      if (!clientId) {
        console.error("Client not found for WebSocket message");
        return;
      }
      
      // Update last activity
      const client = this.clients.get(clientId);
      if (client) {
        client.lastActivity = new Date();
      }
      
      console.log(`📨 WebSocket message from ${userId}:`, data.type);
      
      switch (data.type) {
        case "ping":
          this.sendToClient(clientId, { type: "pong", timestamp: new Date().toISOString() });
          break;
          
        case "cursor_position":
          this.handleCursorPosition(clientId, data);
          break;
          
        case "text_change":
          this.handleTextChange(clientId, data);
          break;
          
        case "file_open":
          this.handleFileOpen(clientId, data);
          break;
          
        case "file_close":
          this.handleFileClose(clientId, data);
          break;
          
        case "selection_change":
          this.handleSelectionChange(clientId, data);
          break;
          
        case "chat_message":
          this.handleChatMessage(clientId, data);
          break;
          
        case "voice_call":
          this.handleVoiceCall(clientId, data);
          break;
          
        case "screen_share":
          this.handleScreenShare(clientId, data);
          break;
          
        case "extension_event":
          this.handleExtensionEvent(clientId, data);
          break;
          
        default:
          console.warn(`Unknown WebSocket message type: ${data.type}`);
      }
      
    } catch (error) {
      console.error("Error handling WebSocket message:", error);
    }
  }
  
  handleDisconnection(ws: ServerWebSocket<WebSocketData>, code: number, message: string) {
    const clientId = this.findClientId(ws);
    
    if (!clientId) {
      console.error("Client not found for WebSocket disconnection");
      return;
    }
    
    const client = this.clients.get(clientId);
    if (!client) return;
    
    const { userId, sessionId } = client;
    
    console.log(`🔌 WebSocket disconnected: ${userId} (${clientId}) - Code: ${code}`);
    
    // Remove from session
    const sessionClients = this.sessions.get(sessionId);
    if (sessionClients) {
      sessionClients.delete(clientId);
      if (sessionClients.size === 0) {
        this.sessions.delete(sessionId);
      }
    }
    
    // Remove client
    this.clients.delete(clientId);
    
    // Notify other clients in the session
    this.broadcastToSession(sessionId, {
      type: "user_left",
      userId,
      clientId,
      timestamp: new Date().toISOString()
    });
  }
  
  private findClientId(ws: ServerWebSocket<WebSocketData>): string | null {
    for (const [clientId, client] of this.clients.entries()) {
      if (client.ws === ws) {
        return clientId;
      }
    }
    return null;
  }
  
  private sendToClient(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === 1) { // WebSocket.OPEN
      try {
        client.ws.send(JSON.stringify(data));
      } catch (error) {
        console.error(`Error sending message to client ${clientId}:`, error);
      }
    }
  }
  
  private broadcastToSession(sessionId: string, data: any, excludeClientId?: string) {
    const sessionClients = this.sessions.get(sessionId);
    if (!sessionClients) return;
    
    for (const clientId of sessionClients) {
      if (clientId !== excludeClientId) {
        this.sendToClient(clientId, data);
      }
    }
  }
  
  private sendSessionInfo(clientId: string) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    const sessionClients = this.sessions.get(client.sessionId);
    if (!sessionClients) return;
    
    const participants = Array.from(sessionClients)
      .map(id => this.clients.get(id))
      .filter(c => c)
      .map(c => ({
        clientId: Array.from(this.clients.entries()).find(([id, client]) => client === c)?.[0],
        userId: c!.userId,
        joinedAt: c!.joinedAt.toISOString(),
        lastActivity: c!.lastActivity.toISOString()
      }));
    
    this.sendToClient(clientId, {
      type: "session_info",
      sessionId: client.sessionId,
      participants,
      timestamp: new Date().toISOString()
    });
  }
  
  private handleCursorPosition(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    this.broadcastToSession(client.sessionId, {
      type: "cursor_position",
      userId: client.userId,
      filePath: data.filePath,
      position: data.position,
      selection: data.selection,
      timestamp: new Date().toISOString()
    }, clientId);
  }
  
  private handleTextChange(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    this.broadcastToSession(client.sessionId, {
      type: "text_change",
      userId: client.userId,
      filePath: data.filePath,
      changes: data.changes,
      version: data.version,
      timestamp: new Date().toISOString()
    }, clientId);
  }
  
  private handleFileOpen(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    this.broadcastToSession(client.sessionId, {
      type: "file_open",
      userId: client.userId,
      filePath: data.filePath,
      timestamp: new Date().toISOString()
    }, clientId);
  }
  
  private handleFileClose(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    this.broadcastToSession(client.sessionId, {
      type: "file_close",
      userId: client.userId,
      filePath: data.filePath,
      timestamp: new Date().toISOString()
    }, clientId);
  }
  
  private handleSelectionChange(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    this.broadcastToSession(client.sessionId, {
      type: "selection_change",
      userId: client.userId,
      filePath: data.filePath,
      selection: data.selection,
      timestamp: new Date().toISOString()
    }, clientId);
  }
  
  private handleChatMessage(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    this.broadcastToSession(client.sessionId, {
      type: "chat_message",
      userId: client.userId,
      message: data.message,
      timestamp: new Date().toISOString()
    }, clientId);
  }
  
  private handleVoiceCall(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    // Handle WebRTC signaling for voice calls
    if (data.targetUserId) {
      // Send to specific user
      const targetClient = Array.from(this.clients.values())
        .find(c => c.userId === data.targetUserId && c.sessionId === client.sessionId);
      
      if (targetClient) {
        const targetClientId = Array.from(this.clients.entries())
          .find(([id, c]) => c === targetClient)?.[0];
        
        if (targetClientId) {
          this.sendToClient(targetClientId, {
            type: "voice_call",
            fromUserId: client.userId,
            signalType: data.signalType,
            signalData: data.signalData,
            timestamp: new Date().toISOString()
          });
        }
      }
    }
  }
  
  private handleScreenShare(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    this.broadcastToSession(client.sessionId, {
      type: "screen_share",
      userId: client.userId,
      action: data.action, // start, stop, frame
      frameData: data.frameData,
      timestamp: new Date().toISOString()
    }, clientId);
  }
  
  private handleExtensionEvent(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client) return;
    
    this.broadcastToSession(client.sessionId, {
      type: "extension_event",
      userId: client.userId,
      extensionId: data.extensionId,
      eventType: data.eventType,
      eventData: data.eventData,
      timestamp: new Date().toISOString()
    }, clientId);
  }
  
  private cleanupInactiveConnections() {
    const now = new Date();
    const timeout = 5 * 60 * 1000; // 5 minutes
    
    for (const [clientId, client] of this.clients.entries()) {
      if (now.getTime() - client.lastActivity.getTime() > timeout) {
        console.log(`🧹 Cleaning up inactive connection: ${client.userId} (${clientId})`);
        
        try {
          client.ws.close(1000, "Inactive connection cleanup");
        } catch (error) {
          console.error("Error closing inactive WebSocket:", error);
        }
        
        // Remove from session
        const sessionClients = this.sessions.get(client.sessionId);
        if (sessionClients) {
          sessionClients.delete(clientId);
          if (sessionClients.size === 0) {
            this.sessions.delete(client.sessionId);
          }
        }
        
        // Remove client
        this.clients.delete(clientId);
      }
    }
  }
  
  // Public methods for external use
  public getSessionParticipants(sessionId: string) {
    const sessionClients = this.sessions.get(sessionId);
    if (!sessionClients) return [];
    
    return Array.from(sessionClients)
      .map(clientId => this.clients.get(clientId))
      .filter(client => client)
      .map(client => ({
        userId: client!.userId,
        joinedAt: client!.joinedAt,
        lastActivity: client!.lastActivity
      }));
  }
  
  public broadcastToAllSessions(data: any) {
    for (const sessionId of this.sessions.keys()) {
      this.broadcastToSession(sessionId, data);
    }
  }
  
  public getActiveSessionsCount(): number {
    return this.sessions.size;
  }
  
  public getConnectedClientsCount(): number {
    return this.clients.size;
  }
}
