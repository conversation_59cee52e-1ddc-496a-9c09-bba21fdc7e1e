{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "noEmit": true, "strict": true, "skipLibCheck": true, "types": ["bun-types"], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/routes/*": ["./routes/*"], "@/services/*": ["./services/*"], "@/middleware/*": ["./middleware/*"], "@/shared/*": ["../../shared/*"]}}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist"]}