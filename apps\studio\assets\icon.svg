<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Background gradient -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e1e1e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2d2d2d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
    </linearGradient>
    
    <!-- Lightning gradient -->
    <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FACC15;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FDE047;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EAB308;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow filter -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Outer ring -->
  <circle cx="256" cy="256" r="220" fill="none" stroke="#333333" stroke-width="2" opacity="0.5"/>
  
  <!-- Lightning bolt -->
  <g transform="translate(256, 256)">
    <!-- Main lightning shape -->
    <path d="M-60,-120 L20,-30 L-20,-30 L60,120 L-20,30 L20,30 Z" 
          fill="url(#lightningGradient)" 
          filter="url(#glow)"
          stroke="#F59E0B" 
          stroke-width="2"/>
    
    <!-- Inner highlight -->
    <path d="M-45,-90 L10,-25 L-10,-25 L45,90 L-10,25 L10,25 Z" 
          fill="#FEF3C7" 
          opacity="0.6"/>
    
    <!-- Core highlight -->
    <path d="M-30,-60 L5,-20 L-5,-20 L30,60 L-5,20 L5,20 Z" 
          fill="#FFFBEB" 
          opacity="0.8"/>
  </g>
  
  <!-- Decorative elements -->
  <g opacity="0.3">
    <!-- Top sparks -->
    <circle cx="200" cy="120" r="3" fill="#FACC15">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" begin="0s"/>
    </circle>
    <circle cx="320" cy="140" r="2" fill="#FDE047">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2.5s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    
    <!-- Bottom sparks -->
    <circle cx="180" cy="380" r="2" fill="#F59E0B">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" repeatCount="indefinite" begin="1s"/>
    </circle>
    <circle cx="340" cy="360" r="3" fill="#FACC15">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2.2s" repeatCount="indefinite" begin="1.5s"/>
    </circle>
  </g>
  
  <!-- Text elements (for larger sizes) -->
  <g opacity="0.8">
    <!-- "K" monogram in bottom right -->
    <text x="400" y="450" 
          font-family="system-ui, -apple-system, sans-serif" 
          font-size="48" 
          font-weight="bold" 
          fill="#FACC15" 
          opacity="0.4">K</text>
  </g>
</svg>
