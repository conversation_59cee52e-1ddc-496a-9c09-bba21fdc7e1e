<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e1e1e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2d2d2d;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FACC15;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FDE047;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EAB308;stop-opacity:1" />
    </linearGradient>
    
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bgGradient)" rx="12"/>
  
  <!-- Lightning bolt -->
  <g transform="translate(200, 120)">
    <path d="M-15,-40 L5,-10 L-5,-10 L15,40 L-5,10 L5,10 Z" 
          fill="url(#lightningGradient)" 
          filter="url(#glow)">
      <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="scale"
        values="1;1.1;1"
        dur="2s"
        repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Title -->
  <text x="200" y="200" 
        text-anchor="middle" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="24" 
        font-weight="bold" 
        fill="#FACC15">
    KodeKilat Studio
    <animate attributeName="opacity" values="0;1;1;0" dur="3s" repeatCount="indefinite"/>
  </text>
  
  <!-- Subtitle -->
  <text x="200" y="225" 
        text-anchor="middle" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="12" 
        fill="#9CA3AF">
    IDE Nusantara Modern
    <animate attributeName="opacity" values="0;0;1;1;0" dur="3s" repeatCount="indefinite"/>
  </text>
  
  <!-- Loading dots -->
  <g transform="translate(200, 250)">
    <circle cx="-15" cy="0" r="3" fill="#FACC15">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>
    </circle>
    <circle cx="0" cy="0" r="3" fill="#FACC15">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="15" cy="0" r="3" fill="#FACC15">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="1s"/>
    </circle>
  </g>
</svg>
