{"name": "@kodekilat/studio", "version": "1.0.0", "description": "KodeKilat Studio - Electron Main Process", "main": "dist/main.js", "scripts": {"dev": "concurrently \"tsc -w\" \"wait-on dist/main.js && electron .\"", "build": "tsc", "start": "electron .", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"electron-is-dev": "^2.0.0", "electron-store": "^8.1.0", "electron-updater": "^6.1.7"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "electron": "^28.0.0", "rimraf": "^5.0.5", "typescript": "^5.3.0", "wait-on": "^7.2.0"}}