import { contextBridge, ipc<PERSON>enderer } from 'electron';

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // File system operations
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  openFolder: () => ipcRenderer.invoke('dialog:openFolder'),
  saveFile: () => ipcRenderer.invoke('dialog:saveFile'),

  // Store operations
  store: {
    get: (key: string) => ipcRenderer.invoke('store:get', key),
    set: (key: string, value: any) => ipcRenderer.invoke('store:set', key, value),
  },

  // Window operations
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipc<PERSON>enderer.invoke('window:close'),
  },

  // Menu event listeners
  onMenuAction: (callback: (channel: string, ...args: any[]) => void) => {
    const menuChannels = [
      'menu:file:new',
      'menu:file:open',
      'menu:file:open-folder',
      'menu:file:save',
      'menu:file:save-as',
      'menu:edit:find',
      'menu:edit:replace',
      'menu:view:toggle-sidebar',
      'menu:view:command-palette',
      'menu:terminal:new',
      'menu:terminal:kill',
      'menu:help:about',
    ];

    menuChannels.forEach(channel => {
      ipcRenderer.on(channel, (_, ...args) => callback(channel, ...args));
    });

    // Return cleanup function
    return () => {
      menuChannels.forEach(channel => {
        ipcRenderer.removeAllListeners(channel);
      });
    };
  },

  // Platform info
  platform: process.platform,

  // Version info
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron,
  },
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}
