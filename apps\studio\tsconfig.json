{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./", "baseUrl": ".", "paths": {"@/*": ["../../*"], "@/shared/*": ["../../shared/*"]}, "strict": true, "noEmit": false, "declaration": false, "sourceMap": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true}, "include": ["*.ts", "**/*.ts"], "exclude": ["node_modules", "dist", "ui"]}