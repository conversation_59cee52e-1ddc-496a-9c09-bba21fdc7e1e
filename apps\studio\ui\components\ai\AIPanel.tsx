import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Send, 
  Bot, 
  User, 
  Spark<PERSON>, 
  <PERSON>, 
  FileText,
  Lightbulb,
  Trash2,
  <PERSON><PERSON><PERSON>,
  Copy,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  codeBlocks?: { language: string; code: string }[];
}

interface AIPanelProps {
  currentFile: string | null;
}

export function AIPanel({ currentFile }: AIPanelProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Halo! Saya KodeKilat AI ⚡, asisten coding Nusantara yang siap membantu Anda. Apa yang bisa saya bantu hari ini?',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: `Terima kasih atas pertanyaan Anda! ${currentFile ? `Saya melihat Anda sedang bekerja dengan file ${currentFile}.` : ''} Berikut adalah respons saya:\n\nSaya dapat membantu Anda dengan:\n- Menulis dan memperbaiki kode\n- Menjelaskan konsep programming\n- Debugging dan optimisasi\n- Code review dan suggestions\n\nAda yang spesifik yang ingin Anda tanyakan?`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        type: 'ai',
        content: 'Chat telah dibersihkan. Ada yang bisa saya bantu?',
        timestamp: new Date()
      }
    ]);
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const formatMessage = (content: string) => {
    // Simple markdown-like formatting
    return content.split('\n').map((line, index) => (
      <div key={index} className={line.startsWith('-') ? 'ml-4' : ''}>
        {line}
      </div>
    ));
  };

  return (
    <div className="h-full flex flex-col">
      {/* Chat Header */}
      <div className="p-3 border-b border-border bg-secondary/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Sparkles size={16} className="text-yellow-500" />
            <span className="text-sm font-medium">AI Chat</span>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={clearChat}
              className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors"
              title="Clear chat"
            >
              <Trash2 size={14} />
            </button>
            <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
              <Settings size={14} />
            </button>
          </div>
        </div>
        {currentFile && (
          <div className="mt-2 text-xs text-muted-foreground">
            Context: {currentFile}
          </div>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-auto p-3 space-y-4">
        {messages.map((message) => (
          <motion.div
            key={message.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex space-x-2 max-w-[85%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {/* Avatar */}
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.type === 'user' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-yellow-500/20 text-yellow-500'
              }`}>
                {message.type === 'user' ? <User size={16} /> : <Bot size={16} />}
              </div>

              {/* Message Content */}
              <div className={`rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-accent text-accent-foreground'
              }`}>
                <div className="text-sm whitespace-pre-wrap">
                  {formatMessage(message.content)}
                </div>
                
                {/* Message Actions */}
                {message.type === 'ai' && (
                  <div className="flex items-center space-x-2 mt-2 pt-2 border-t border-border/50">
                    <button
                      onClick={() => copyMessage(message.content)}
                      className="p-1 hover:bg-background/20 rounded transition-colors"
                      title="Copy message"
                    >
                      <Copy size={12} />
                    </button>
                    <button className="p-1 hover:bg-background/20 rounded transition-colors">
                      <ThumbsUp size={12} />
                    </button>
                    <button className="p-1 hover:bg-background/20 rounded transition-colors">
                      <ThumbsDown size={12} />
                    </button>
                  </div>
                )}
                
                <div className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
        
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex justify-start"
          >
            <div className="flex space-x-2">
              <div className="w-8 h-8 rounded-full bg-yellow-500/20 text-yellow-500 flex items-center justify-center">
                <Bot size={16} />
              </div>
              <div className="bg-accent text-accent-foreground rounded-lg p-3">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-3 border-t border-border">
        <div className="flex space-x-2">
          <div className="flex-1 relative">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Tanya KodeKilat AI..."
              className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary min-h-[40px] max-h-[120px]"
              rows={1}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send size={16} />
          </button>
        </div>
        
        {/* Quick Actions */}
        <div className="flex items-center space-x-2 mt-2">
          <button className="text-xs px-2 py-1 bg-accent hover:bg-accent/80 rounded transition-colors">
            <Code size={12} className="inline mr-1" />
            Explain Code
          </button>
          <button className="text-xs px-2 py-1 bg-accent hover:bg-accent/80 rounded transition-colors">
            <FileText size={12} className="inline mr-1" />
            Generate Docs
          </button>
          <button className="text-xs px-2 py-1 bg-accent hover:bg-accent/80 rounded transition-colors">
            <Lightbulb size={12} className="inline mr-1" />
            Optimize
          </button>
        </div>
      </div>
    </div>
  );
}
