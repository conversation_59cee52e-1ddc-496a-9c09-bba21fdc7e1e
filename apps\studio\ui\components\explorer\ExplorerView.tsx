import { FileSystemItem, useFileSystem } from '@/packages/fs/useFileSystem';
import {
  AlertCircle,
  ChevronDown,
  ChevronRight,
  File,
  FilePlus,
  Folder,
  FolderOpen,
  FolderPlus,
  MoreHorizontal,
  RefreshCw,
  Users,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface ExplorerViewProps {
  onFileSelect: (file: string) => void;
}

export function ExplorerView({ onFileSelect }: ExplorerViewProps) {
  const {
    files,
    loading,
    error,
    currentPath,
    listDirectory,
    createDirectory,
    deleteItem,
    renameItem,
    refreshDirectory,
    openFolder,
  } = useFileSystem();

  // Remote collaboration integration
  // const { activeFiles, fileEditors, isConnected: isRemoteConnected } = useRemoteFileExplorer();
  const activeFiles: string[] = [];
  const fileEditors: Record<string, any[]> = {};
  const isRemoteConnected = false;

  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    item: FileSystemItem;
  } | null>(null);

  // Handle folder toggle
  const toggleFolder = (path: string) => {
    setExpandedFolders((prev: Set<string>) => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  // Handle file/folder selection
  const handleItemClick = (item: FileSystemItem) => {
    setSelectedItem(item.path);
    if (item.type === 'file') {
      onFileSelect(item.path);
    } else {
      toggleFolder(item.path);
    }
  };

  // Handle context menu
  const handleContextMenu = (e: React.MouseEvent, item: FileSystemItem) => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      item,
    });
  };

  // Close context menu
  const closeContextMenu = () => {
    setContextMenu(null);
  };

  // Handle folder open
  const handleOpenFolder = async () => {
    await openFolder();
  };

  // Handle new file creation
  const handleNewFile = async () => {
    const fileName = prompt('Enter file name:');
    if (fileName && currentPath) {
      try {
        const filePath = `${currentPath}/${fileName}`;
        // Create empty file by writing empty content
        await fetch('http://localhost:3001/api/fs/write', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ path: filePath, content: '' }),
        });
        await refreshDirectory();
      } catch (err) {
        console.error('Error creating file:', err);
      }
    }
  };

  // Handle new folder creation
  const handleNewFolder = async () => {
    const folderName = prompt('Enter folder name:');
    if (folderName && currentPath) {
      try {
        const folderPath = `${currentPath}/${folderName}`;
        await createDirectory(folderPath);
      } catch (err) {
        console.error('Error creating folder:', err);
      }
    }
  };

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      closeContextMenu();
    };

    if (contextMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [contextMenu]);

  // Render file item with proper typing
  const renderFileItem = (item: FileSystemItem, depth: number = 0) => {
    const isExpanded = expandedFolders.has(item.path);
    const isSelected = selectedItem === item.path;

    return (
      <div key={item.path}>
        <div
          className={`flex items-center hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors py-1 px-2 rounded-sm mx-1 ${
            isSelected ? 'bg-accent text-accent-foreground' : ''
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => handleItemClick(item)}
          onContextMenu={e => handleContextMenu(e, item)}
        >
          {/* Folder Toggle Icon */}
          {item.type === 'directory' && (
            <div className="mr-1 flex-shrink-0">
              {isExpanded ? (
                <ChevronDown size={16} className="text-muted-foreground" />
              ) : (
                <ChevronRight size={16} className="text-muted-foreground" />
              )}
            </div>
          )}

          {/* File/Folder Icon */}
          <div className="mr-2 flex-shrink-0">
            {item.type === 'directory' ? (
              isExpanded ? (
                <FolderOpen size={16} className="text-blue-400" />
              ) : (
                <Folder size={16} className="text-blue-400" />
              )
            ) : (
              <File size={16} className="text-muted-foreground" />
            )}
          </div>

          {/* File/Folder Name */}
          <span className="text-sm truncate flex-1">{item.name}</span>

          {/* Remote Collaboration Indicators */}
          {isRemoteConnected && item.type === 'file' && (
            <div className="flex items-center space-x-1 ml-2">
              {/* Active file indicator */}
              {activeFiles.includes(item.path) && (
                <div className="w-2 h-2 bg-green-500 rounded-full" title="File is being edited" />
              )}

              {/* Show participant count if file has editors */}
              {fileEditors[item.path] && fileEditors[item.path].length > 0 && (
                <div className="flex items-center">
                  <Users size={12} className="text-blue-400" />
                  <span className="text-xs text-blue-400 ml-1">
                    {fileEditors[item.path].length}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Render children if directory is expanded */}
        {item.type === 'directory' && isExpanded && item.children && (
          <div>{item.children.map(child => renderFileItem(child, depth + 1))}</div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-4 flex items-center justify-center">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <RefreshCw size={16} className="animate-spin" />
          <span>Loading workspace...</span>
        </div>
      </div>
    );
  }

  if (!currentPath) {
    return (
      <div className="p-4 text-center">
        <div className="text-sm text-muted-foreground mb-4">No folder opened</div>
        <button
          onClick={handleOpenFolder}
          className="px-3 py-2 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90 transition-colors"
        >
          Open Folder
        </button>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <div className="flex items-center justify-center space-x-2 text-sm text-red-500 mb-4">
          <AlertCircle size={16} />
          <span>Error loading files</span>
        </div>
        <div className="text-xs text-muted-foreground mb-4">{error}</div>
        <button
          onClick={refreshDirectory}
          className="px-3 py-2 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Workspace Header */}
      <div className="p-2 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm font-medium">
            <FolderOpen size={16} />
            <span className="truncate">{currentPath.split('/').pop() || 'Workspace'}</span>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={handleNewFile}
              className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors"
              title="New File"
            >
              <FilePlus size={14} />
            </button>
            <button
              onClick={handleNewFolder}
              className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors"
              title="New Folder"
            >
              <FolderPlus size={14} />
            </button>
            <button
              onClick={refreshDirectory}
              className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors"
              title="Refresh"
            >
              <RefreshCw size={14} />
            </button>
            <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
              <MoreHorizontal size={14} />
            </button>
          </div>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-auto">
        <div className="py-1">{files.map(item => renderFileItem(item))}</div>
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <div
          className="fixed bg-popover border border-border rounded-md shadow-lg py-1 z-50"
          style={{ left: contextMenu.x, top: contextMenu.y }}
        >
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-accent hover:text-accent-foreground"
            onClick={() => {
              // Handle rename
              closeContextMenu();
            }}
          >
            Rename
          </button>
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-accent hover:text-accent-foreground text-red-500"
            onClick={() => {
              if (confirm(`Delete ${contextMenu.item.name}?`)) {
                deleteItem(contextMenu.item.path);
              }
              closeContextMenu();
            }}
          >
            Delete
          </button>
        </div>
      )}
    </div>
  );
}
