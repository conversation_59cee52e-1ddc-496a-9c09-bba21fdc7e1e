import React, { useState } from 'react';
import { Header } from './Header';
import { SidebarLeft } from './SidebarLeft';
import { SidebarRight } from './SidebarRight';
import { Footer } from './Footer';
import { EditorWrapper } from '../../../../../packages/editor/EditorWrapper';
import { motion } from 'framer-motion';

interface MainLayoutProps {
  className?: string;
}

export default function MainLayout({ className }: MainLayoutProps) {
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(true);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(true);
  const [currentFile, setCurrentFile] = useState<string | null>(null);

  return (
    <div className={`flex flex-col h-screen bg-background text-foreground ${className}`}>
      {/* Header Menu Bar */}
      <Header 
        onToggleLeftSidebar={() => setLeftSidebarOpen(!leftSidebarOpen)}
        onToggleRightSidebar={() => setRightSidebarOpen(!rightSidebarOpen)}
        leftSidebarOpen={leftSidebarOpen}
        rightSidebarOpen={rightSidebarOpen}
      />
      
      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Explorer, Git, Extensions */}
        <motion.div
          initial={false}
          animate={{ width: leftSidebarOpen ? 300 : 0 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
          className="overflow-hidden border-r border-border"
        >
          <SidebarLeft 
            isOpen={leftSidebarOpen}
            onFileSelect={setCurrentFile}
          />
        </motion.div>
        
        {/* Main Editor Area */}
        <main className="flex-1 flex flex-col min-w-0">
          <div className="flex-1 overflow-hidden">
            <EditorWrapper 
              currentFile={currentFile}
              onFileChange={setCurrentFile}
            />
          </div>
        </main>
        
        {/* Right Sidebar - KodeKilat AI */}
        <motion.div
          initial={false}
          animate={{ width: rightSidebarOpen ? 350 : 0 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
          className="overflow-hidden border-l border-border"
        >
          <SidebarRight 
            isOpen={rightSidebarOpen}
            currentFile={currentFile}
          />
        </motion.div>
      </div>
      
      {/* Footer Status Bar */}
      <Footer 
        currentFile={currentFile}
        leftSidebarOpen={leftSidebarOpen}
        rightSidebarOpen={rightSidebarOpen}
      />
    </div>
  );
}
