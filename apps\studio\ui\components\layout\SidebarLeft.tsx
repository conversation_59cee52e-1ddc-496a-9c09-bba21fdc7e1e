import { motion } from 'framer-motion';
import {
  ChevronDown,
  ChevronRight,
  Files,
  GitBranch,
  Package,
  Palette,
  Search,
  Settings,
  User,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { ExplorerView } from '../explorer/ExplorerView';

interface SidebarLeftProps {
  isOpen: boolean;
  onFileSelect: (file: string) => void;
}

export function SidebarLeft({ isOpen, onFileSelect }: SidebarLeftProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('explorer');
  const [expandedSections, setExpandedSections] = useState<string[]>(['explorer', 'remote']);

  const tabs = [
    { id: 'explorer', icon: Files, label: 'Explorer', shortcut: 'Ctrl+Shift+E' },
    { id: 'search', icon: Search, label: 'Search', shortcut: 'Ctrl+Shift+F' },
    { id: 'ui-designer', icon: Palette, label: 'UI Designer', shortcut: 'Ctrl+Shift+U' },
    { id: 'git', icon: GitBranch, label: 'Source Control', shortcut: 'Ctrl+Shift+G' },
    { id: 'remote', icon: Users, label: 'Collaboration', shortcut: 'Ctrl+Shift+R' },
    { id: 'extensions', icon: Package, label: 'Extensions', shortcut: 'Ctrl+Shift+X' },
    { id: 'settings', icon: Settings, label: 'Settings', shortcut: 'Ctrl+,' },
    { id: 'profile', icon: User, label: 'Profile', shortcut: '' },
  ];

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId) ? prev.filter(id => id !== sectionId) : [...prev, sectionId]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="flex h-full bg-background">
      {/* Tab Icons */}
      <div className="w-12 bg-secondary border-r border-border flex flex-col">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`p-3 hover:bg-accent hover:text-accent-foreground transition-colors relative group ${
              activeTab === tab.id ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'
            }`}
            title={`${tab.label} ${tab.shortcut}`}
          >
            <tab.icon size={20} />
            {activeTab === tab.id && (
              <motion.div
                layoutId="activeTab"
                className="absolute left-0 top-0 bottom-0 w-0.5 bg-primary"
                initial={false}
                transition={{ duration: 0.2 }}
              />
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 flex flex-col">
        {/* Tab Header */}
        <div className="h-8 flex items-center px-3 bg-secondary border-b border-border">
          <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            {tabs.find(tab => tab.id === activeTab)?.label}
          </span>
        </div>

        {/* Tab Content Area */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'explorer' && (
            <div className="h-full">
              {/* Explorer Header */}
              <div className="p-2 border-b border-border">
                <button
                  onClick={() => toggleSection('explorer')}
                  className="flex items-center w-full text-sm font-medium hover:bg-accent hover:text-accent-foreground rounded px-2 py-1 transition-colors"
                >
                  {expandedSections.includes('explorer') ? (
                    <ChevronDown size={16} className="mr-1" />
                  ) : (
                    <ChevronRight size={16} className="mr-1" />
                  )}
                  EXPLORER
                </button>
              </div>

              {/* Explorer Content */}
              {expandedSections.includes('explorer') && (
                <div className="flex-1 overflow-auto">
                  <ExplorerView onFileSelect={onFileSelect} />
                </div>
              )}
            </div>
          )}

          {activeTab === 'search' && (
            <div className="p-4">
              <div className="space-y-4">
                <div>
                  <input
                    type="text"
                    placeholder="Search files..."
                    className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Replace..."
                    className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div className="text-sm text-muted-foreground">No results found</div>
              </div>
            </div>
          )}

          {activeTab === 'ui-designer' && (
            <div className="p-4">
              <div className="space-y-4">
                <div className="text-center">
                  <Palette size={48} className="mx-auto mb-4 text-yellow-500" />
                  <h3 className="font-medium mb-2">UI Designer ⚡</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Visual interface builder dengan drag & drop
                  </p>
                  <button
                    onClick={() => {
                      if (typeof window !== 'undefined') {
                        window.open('/ui-designer', '_blank');
                      }
                    }}
                    className="w-full px-3 py-2 bg-yellow-500 text-black rounded-md text-sm hover:bg-yellow-400 transition-colors font-medium"
                  >
                    Open UI Designer
                  </button>
                </div>
                <div className="border-t border-border pt-4">
                  <h4 className="text-sm font-medium mb-2">Features:</h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• Drag & drop components</li>
                    <li>• Live preview</li>
                    <li>• Code generation</li>
                    <li>• Responsive design</li>
                    <li>• Export to React/HTML</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'git' && (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                <div className="mb-4">
                  <h3 className="font-medium mb-2">Source Control</h3>
                  <p>Initialize repository to start tracking changes</p>
                </div>
                <button className="px-3 py-2 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90 transition-colors">
                  Initialize Repository
                </button>
              </div>
            </div>
          )}

          {activeTab === 'remote' && (
            <div className="h-full flex flex-col">
              {/* Remote Collaboration Header */}
              <div className="p-2 border-b border-border">
                <button
                  onClick={() => toggleSection('remote')}
                  className="flex items-center w-full text-sm font-medium hover:bg-accent hover:text-accent-foreground rounded px-2 py-1 transition-colors"
                >
                  {expandedSections.includes('remote') ? (
                    <ChevronDown size={16} className="mr-1" />
                  ) : (
                    <ChevronRight size={16} className="mr-1" />
                  )}
                  COLLABORATION ⚡
                </button>
              </div>

              {/* Remote Collaboration Content */}
              {expandedSections.includes('remote') && (
                <div className="flex-1 overflow-auto">
                  <RemoteSessionManager />
                </div>
              )}
            </div>
          )}

          {activeTab === 'extensions' && (
            <div className="p-4">
              <div className="space-y-4">
                <div>
                  <button
                    onClick={() => router.push('/extensions')}
                    className="w-full px-3 py-2 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90 transition-colors"
                  >
                    Open Extensions Manager
                  </button>
                </div>
                <div className="text-sm text-muted-foreground">
                  <h3 className="font-medium mb-2">Quick Actions</h3>
                  <div className="space-y-2">
                    <button
                      onClick={() => router.push('/extensions?tab=marketplace')}
                      className="w-full p-2 border border-border rounded hover:bg-accent transition-colors text-left"
                    >
                      <div className="font-medium">Browse Marketplace</div>
                      <div className="text-xs text-muted-foreground">Find new extensions</div>
                    </button>
                    <button
                      onClick={() => router.push('/extensions?tab=create')}
                      className="w-full p-2 border border-border rounded hover:bg-accent transition-colors text-left"
                    >
                      <div className="font-medium">Create Extension</div>
                      <div className="text-xs text-muted-foreground">Build your own .kodix</div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="p-4">
              <div className="text-sm">
                <h3 className="font-medium mb-4">Settings</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium mb-1">Theme</label>
                    <select className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm">
                      <option>Dark (Default)</option>
                      <option>Light</option>
                      <option>Auto</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Font Size</label>
                    <input
                      type="number"
                      defaultValue={14}
                      className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="p-4">
              <div className="text-sm">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <User size={20} className="text-primary-foreground" />
                  </div>
                  <div>
                    <div className="font-medium">Developer</div>
                    <div className="text-xs text-muted-foreground">Local User</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <button className="w-full text-left px-3 py-2 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                    Sync Settings
                  </button>
                  <button className="w-full text-left px-3 py-2 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                    Backup Data
                  </button>
                  <button className="w-full text-left px-3 py-2 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
