import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Bot, 
  MessageSquare, 
  Code, 
  Lightbulb, 
  Settings, 
  Send,
  Sparkles,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { AIPanel } from '../ai/AIPanel';

interface SidebarRightProps {
  isOpen: boolean;
  currentFile: string | null;
}

export function SidebarRight({ isOpen, currentFile }: SidebarRightProps) {
  const [activeTab, setActiveTab] = useState('ai');
  const [expandedSections, setExpandedSections] = useState<string[]>(['chat', 'suggestions']);

  const tabs = [
    { id: 'ai', icon: Bot, label: 'KodeKilat AI', color: 'text-yellow-500' },
    { id: 'suggestions', icon: Lightbulb, label: 'Suggestions', color: 'text-blue-500' },
    { id: 'code-actions', icon: Code, label: 'Code Actions', color: 'text-green-500' },
  ];

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="h-12 flex items-center px-4 border-b border-border bg-secondary">
        <div className="flex items-center space-x-2">
          <Sparkles size={20} className="text-yellow-500" />
          <span className="font-semibold text-sm">KodeKilat AI</span>
          <span className="text-xs bg-yellow-500/20 text-yellow-500 px-2 py-0.5 rounded-full">
            BETA
          </span>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-border bg-secondary">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 text-sm transition-colors relative ${
              activeTab === tab.id 
                ? 'bg-background text-foreground' 
                : 'text-muted-foreground hover:text-foreground hover:bg-accent'
            }`}
          >
            <tab.icon size={16} className={tab.color} />
            <span className="hidden lg:inline">{tab.label}</span>
            {activeTab === tab.id && (
              <motion.div
                layoutId="activeRightTab"
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
                initial={false}
                transition={{ duration: 0.2 }}
              />
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'ai' && (
          <div className="h-full flex flex-col">
            {/* AI Chat Section */}
            <div className="border-b border-border">
              <button
                onClick={() => toggleSection('chat')}
                className="flex items-center w-full p-3 text-sm font-medium hover:bg-accent transition-colors"
              >
                {expandedSections.includes('chat') ? (
                  <ChevronDown size={16} className="mr-2" />
                ) : (
                  <ChevronRight size={16} className="mr-2" />
                )}
                <MessageSquare size={16} className="mr-2" />
                AI CHAT
              </button>
              
              {expandedSections.includes('chat') && (
                <div className="flex-1 flex flex-col">
                  <AIPanel currentFile={currentFile} />
                </div>
              )}
            </div>

            {/* AI Suggestions Section */}
            <div className="border-b border-border">
              <button
                onClick={() => toggleSection('suggestions')}
                className="flex items-center w-full p-3 text-sm font-medium hover:bg-accent transition-colors"
              >
                {expandedSections.includes('suggestions') ? (
                  <ChevronDown size={16} className="mr-2" />
                ) : (
                  <ChevronRight size={16} className="mr-2" />
                )}
                <Lightbulb size={16} className="mr-2" />
                SUGGESTIONS
              </button>
              
              {expandedSections.includes('suggestions') && (
                <div className="p-3 space-y-2">
                  <div className="p-3 bg-accent/50 rounded-lg border border-border">
                    <div className="flex items-start space-x-2">
                      <Lightbulb size={16} className="text-yellow-500 mt-0.5" />
                      <div className="flex-1">
                        <div className="text-sm font-medium">Optimize imports</div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Remove unused imports in {currentFile || 'current file'}
                        </div>
                        <button className="text-xs text-primary hover:underline mt-2">
                          Apply suggestion
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-3 bg-accent/50 rounded-lg border border-border">
                    <div className="flex items-start space-x-2">
                      <Code size={16} className="text-blue-500 mt-0.5" />
                      <div className="flex-1">
                        <div className="text-sm font-medium">Add error handling</div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Consider adding try-catch blocks
                        </div>
                        <button className="text-xs text-primary hover:underline mt-2">
                          Show examples
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* AI Settings */}
            <div className="p-3 border-b border-border">
              <button className="flex items-center w-full text-sm text-muted-foreground hover:text-foreground transition-colors">
                <Settings size={16} className="mr-2" />
                AI Settings
              </button>
            </div>
          </div>
        )}

        {activeTab === 'suggestions' && (
          <div className="p-4">
            <div className="space-y-3">
              <div className="text-sm">
                <h3 className="font-medium mb-3">Code Suggestions</h3>
                <div className="space-y-2">
                  <div className="p-3 border border-border rounded-lg">
                    <div className="font-medium text-sm">Performance Optimization</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Use React.memo for component optimization
                    </div>
                  </div>
                  <div className="p-3 border border-border rounded-lg">
                    <div className="font-medium text-sm">Code Quality</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Add TypeScript strict mode
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'code-actions' && (
          <div className="p-4">
            <div className="space-y-3">
              <div className="text-sm">
                <h3 className="font-medium mb-3">Quick Actions</h3>
                <div className="space-y-2">
                  <button className="w-full text-left p-3 border border-border rounded-lg hover:bg-accent transition-colors">
                    <div className="font-medium text-sm">Extract Component</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Extract selected JSX to new component
                    </div>
                  </button>
                  <button className="w-full text-left p-3 border border-border rounded-lg hover:bg-accent transition-colors">
                    <div className="font-medium text-sm">Generate Tests</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Auto-generate unit tests for current file
                    </div>
                  </button>
                  <button className="w-full text-left p-3 border border-border rounded-lg hover:bg-accent transition-colors">
                    <div className="font-medium text-sm">Add Documentation</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Generate JSDoc comments
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
