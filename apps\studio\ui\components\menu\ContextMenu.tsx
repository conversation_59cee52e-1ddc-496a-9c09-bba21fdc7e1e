import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowRight,
  Clipboard,
  Copy,
  Edit,
  Eye,
  FileText,
  Folder,
  Info,
  Minus,
  RefreshCw,
  RotateCcw,
  RotateCw,
  Scissors,
  Search,
  Settings,
  Trash2,
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

interface ContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number };
  onClose: () => void;
  contextType: 'editor' | 'explorer' | 'tab' | 'general';
  targetData?: any;
}

interface MenuItem {
  id: string;
  label?: string;
  icon?: React.ComponentType<any>;
  shortcut?: string;
  disabled?: boolean;
  separator?: boolean;
  submenu?: MenuItem[];
  action?: () => void;
}

export function ContextMenu({
  isOpen,
  position,
  onClose,
  contextType,
  targetData,
}: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  const [submenuOpen, setSubmenuOpen] = useState<string | null>(null);

  const getMenuItems = (): MenuItem[] => {
    switch (contextType) {
      case 'editor':
        return [
          {
            id: 'cut',
            label: 'Cut',
            icon: Scissors,
            shortcut: 'Ctrl+X',
            action: () => console.log('Cut'),
          },
          {
            id: 'copy',
            label: 'Copy',
            icon: Copy,
            shortcut: 'Ctrl+C',
            action: () => console.log('Copy'),
          },
          {
            id: 'paste',
            label: 'Paste',
            icon: Clipboard,
            shortcut: 'Ctrl+V',
            action: () => console.log('Paste'),
          },
          { id: 'separator1', separator: true },
          {
            id: 'undo',
            label: 'Undo',
            icon: RotateCcw,
            shortcut: 'Ctrl+Z',
            action: () => console.log('Undo'),
          },
          {
            id: 'redo',
            label: 'Redo',
            icon: RotateCw,
            shortcut: 'Ctrl+Y',
            action: () => console.log('Redo'),
          },
          { id: 'separator2', separator: true },
          {
            id: 'find',
            label: 'Find',
            icon: Search,
            shortcut: 'Ctrl+F',
            action: () => console.log('Find'),
          },
          {
            id: 'replace',
            label: 'Replace',
            icon: Search,
            shortcut: 'Ctrl+H',
            action: () => console.log('Replace'),
          },
          { id: 'separator3', separator: true },
          {
            id: 'format',
            label: 'Format Document',
            icon: Edit,
            shortcut: 'Shift+Alt+F',
            action: () => console.log('Format Document'),
          },
          {
            id: 'ai-actions',
            label: 'KodeKilat AI',
            icon: ArrowRight,
            submenu: [
              {
                id: 'ai-explain',
                label: 'Explain Code',
                action: () => console.log('AI Explain'),
              },
              {
                id: 'ai-optimize',
                label: 'Optimize Code',
                action: () => console.log('AI Optimize'),
              },
              {
                id: 'ai-comment',
                label: 'Add Comments',
                action: () => console.log('AI Comment'),
              },
              {
                id: 'ai-refactor',
                label: 'Refactor Code',
                action: () => console.log('AI Refactor'),
              },
            ],
          },
        ];

      case 'explorer':
        return [
          {
            id: 'new-file',
            label: 'New File',
            icon: FileText,
            action: () => console.log('New File'),
          },
          {
            id: 'new-folder',
            label: 'New Folder',
            icon: Folder,
            action: () => console.log('New Folder'),
          },
          { id: 'separator1', separator: true },
          {
            id: 'cut',
            label: 'Cut',
            icon: Scissors,
            shortcut: 'Ctrl+X',
            action: () => console.log('Cut File'),
          },
          {
            id: 'copy',
            label: 'Copy',
            icon: Copy,
            shortcut: 'Ctrl+C',
            action: () => console.log('Copy File'),
          },
          {
            id: 'paste',
            label: 'Paste',
            icon: Clipboard,
            shortcut: 'Ctrl+V',
            action: () => console.log('Paste File'),
          },
          { id: 'separator2', separator: true },
          {
            id: 'rename',
            label: 'Rename',
            icon: Edit,
            shortcut: 'F2',
            action: () => console.log('Rename'),
          },
          {
            id: 'delete',
            label: 'Delete',
            icon: Trash2,
            shortcut: 'Delete',
            action: () => console.log('Delete'),
          },
          { id: 'separator3', separator: true },
          {
            id: 'reveal',
            label: 'Reveal in File Explorer',
            icon: Eye,
            action: () => console.log('Reveal in Explorer'),
          },
          {
            id: 'copy-path',
            label: 'Copy Path',
            icon: Copy,
            action: () => console.log('Copy Path'),
          },
          {
            id: 'copy-relative-path',
            label: 'Copy Relative Path',
            icon: Copy,
            action: () => console.log('Copy Relative Path'),
          },
        ];

      case 'tab':
        return [
          {
            id: 'close',
            label: 'Close',
            icon: Minus,
            shortcut: 'Ctrl+W',
            action: () => console.log('Close Tab'),
          },
          {
            id: 'close-others',
            label: 'Close Others',
            icon: Minus,
            action: () => console.log('Close Other Tabs'),
          },
          {
            id: 'close-all',
            label: 'Close All',
            icon: Minus,
            action: () => console.log('Close All Tabs'),
          },
          { id: 'separator1', separator: true },
          {
            id: 'split-right',
            label: 'Split Right',
            icon: ArrowRight,
            action: () => console.log('Split Right'),
          },
          {
            id: 'split-down',
            label: 'Split Down',
            icon: ArrowRight,
            action: () => console.log('Split Down'),
          },
          { id: 'separator2', separator: true },
          {
            id: 'copy-path',
            label: 'Copy Path',
            icon: Copy,
            action: () => console.log('Copy Path'),
          },
          {
            id: 'reveal',
            label: 'Reveal in File Explorer',
            icon: Eye,
            action: () => console.log('Reveal in Explorer'),
          },
        ];

      default:
        return [
          {
            id: 'refresh',
            label: 'Refresh',
            icon: RefreshCw,
            shortcut: 'F5',
            action: () => console.log('Refresh'),
          },
          { id: 'separator1', separator: true },
          {
            id: 'settings',
            label: 'Settings',
            icon: Settings,
            shortcut: 'Ctrl+,',
            action: () => console.log('Settings'),
          },
          {
            id: 'about',
            label: 'About',
            icon: Info,
            action: () => console.log('About'),
          },
        ];
    }
  };

  const menuItems = getMenuItems();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  const handleItemClick = (item: MenuItem) => {
    if (item.action && !item.disabled) {
      item.action();
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="fixed z-50 bg-popover border border-border rounded-md shadow-lg min-w-[200px] py-1"
        style={{
          left: position.x,
          top: position.y,
        }}
      >
        {menuItems.map((item, index) => {
          if (item.separator) {
            return <div key={index} className="h-px bg-border my-1" />;
          }

          return (
            <button
              key={item.id}
              onClick={() => handleItemClick(item)}
              disabled={item.disabled}
              className={`w-full flex items-center justify-between px-3 py-1.5 text-sm text-left transition-colors ${
                item.disabled
                  ? 'text-muted-foreground cursor-not-allowed'
                  : 'hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              <div className="flex items-center space-x-2">
                {item.icon && <item.icon size={16} />}
                <span>{item.label}</span>
              </div>
              {item.shortcut && (
                <span className="text-xs text-muted-foreground">{item.shortcut}</span>
              )}
              {item.submenu && <ArrowRight size={14} className="text-muted-foreground" />}
            </button>
          );
        })}
      </motion.div>
    </AnimatePresence>
  );
}
