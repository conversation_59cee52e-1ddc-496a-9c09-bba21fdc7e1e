import { motion } from 'framer-motion';
import { Download, FileText, FolderOpen, LogOut, Plus, Save, SaveAll, X } from 'lucide-react';

interface FileMenuProps {
  onClose: () => void;
}

export function FileMenu({ onClose }: FileMenuProps) {
  const menuItems = [
    {
      id: 'new-file',
      label: 'New File',
      icon: FileText,
      shortcut: 'Ctrl+N',
      action: () => console.log('New File'),
    },
    {
      id: 'new-folder',
      label: 'New Folder',
      icon: Plus,
      shortcut: 'Ctrl+Shift+N',
      action: () => console.log('New Folder'),
    },
    { type: 'separator' },
    {
      id: 'open-file',
      label: 'Open File...',
      icon: FileText,
      shortcut: 'Ctrl+O',
      action: () => console.log('Open File'),
    },
    {
      id: 'open-folder',
      label: 'Open Folder...',
      icon: FolderOpen,
      shortcut: 'Ctrl+K Ctrl+O',
      action: () => console.log('Open Folder'),
    },
    {
      id: 'open-recent',
      label: 'Open Recent',
      icon: FolderOpen,
      shortcut: '',
      submenu: [
        { label: 'project-1', action: () => console.log('Open project-1') },
        { label: 'project-2', action: () => console.log('Open project-2') },
        { type: 'separator' },
        { label: 'Clear Recently Opened', action: () => console.log('Clear recent') },
      ],
    },
    { type: 'separator' },
    {
      id: 'save',
      label: 'Save',
      icon: Save,
      shortcut: 'Ctrl+S',
      action: () => console.log('Save'),
    },
    {
      id: 'save-as',
      label: 'Save As...',
      icon: Save,
      shortcut: 'Ctrl+Shift+S',
      action: () => console.log('Save As'),
    },
    {
      id: 'save-all',
      label: 'Save All',
      icon: SaveAll,
      shortcut: 'Ctrl+K S',
      action: () => console.log('Save All'),
    },
    { type: 'separator' },
    {
      id: 'export',
      label: 'Export Project',
      icon: Download,
      shortcut: '',
      action: () => console.log('Export'),
    },
    { type: 'separator' },
    {
      id: 'close-file',
      label: 'Close File',
      icon: X,
      shortcut: 'Ctrl+W',
      action: () => console.log('Close File'),
    },
    {
      id: 'close-folder',
      label: 'Close Folder',
      icon: X,
      shortcut: 'Ctrl+K F',
      action: () => console.log('Close Folder'),
    },
    { type: 'separator' },
    {
      id: 'exit',
      label: 'Exit',
      icon: LogOut,
      shortcut: 'Alt+F4',
      action: () => {
        if (typeof window !== 'undefined' && (window as any).electronAPI) {
          (window as any).electronAPI.windowControl('close');
        }
      },
    },
  ];

  const handleItemClick = (item: any) => {
    if (item.action) {
      item.action();
      onClose();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.1 }}
      className="bg-popover border border-border rounded-md shadow-lg py-1 min-w-[200px] z-50"
      onMouseLeave={onClose}
    >
      {menuItems.map((item, index) => {
        if (item.type === 'separator') {
          return <div key={index} className="h-px bg-border my-1" />;
        }

        return (
          <button
            key={item.id}
            onClick={() => handleItemClick(item)}
            className="w-full flex items-center justify-between px-3 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground transition-colors text-left"
          >
            <div className="flex items-center space-x-2">
              {item.icon && <item.icon size={16} />}
              <span>{item.label}</span>
            </div>
            {item.shortcut && (
              <span className="text-xs text-muted-foreground">{item.shortcut}</span>
            )}
          </button>
        );
      })}
    </motion.div>
  );
}
