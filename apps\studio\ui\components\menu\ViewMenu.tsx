import { motion } from 'framer-motion';
import {
  Eye,
  Files,
  GitBranch,
  Layout,
  Maximize,
  Package,
  Palette,
  Search,
  Sidebar,
  Terminal,
} from 'lucide-react';
import { useRouter } from 'next/router';

interface ViewMenuProps {
  onClose: () => void;
}

export function ViewMenu({ onClose }: ViewMenuProps) {
  const router = useRouter();
  const menuItems = [
    {
      id: 'command-palette',
      label: 'Command Palette...',
      icon: Search,
      shortcut: 'Ctrl+Shift+P',
      action: () => console.log('Command Palette'),
    },
    {
      id: 'open-view',
      label: 'Open View...',
      icon: Eye,
      shortcut: '',
      action: () => console.log('Open View'),
    },
    { type: 'separator' },
    {
      id: 'explorer',
      label: 'Explorer',
      icon: Files,
      shortcut: 'Ctrl+Shift+E',
      action: () => console.log('Toggle Explorer'),
    },
    {
      id: 'search',
      label: 'Search',
      icon: Search,
      shortcut: 'Ctrl+Shift+F',
      action: () => console.log('Toggle Search'),
    },
    {
      id: 'source-control',
      label: 'Source Control',
      icon: GitBranch,
      shortcut: 'Ctrl+Shift+G',
      action: () => console.log('Toggle Source Control'),
    },
    {
      id: 'extensions',
      label: 'Extensions',
      icon: Package,
      shortcut: 'Ctrl+Shift+X',
      action: () => router.push('/extensions'),
    },
    {
      id: 'ui-designer',
      label: 'UI Designer',
      icon: Palette,
      shortcut: 'Ctrl+Shift+U',
      action: () => {
        if (typeof window !== 'undefined') {
          window.open('/ui-designer', '_blank');
        }
      },
    },
    { type: 'separator' },
    {
      id: 'terminal',
      label: 'Terminal',
      icon: Terminal,
      shortcut: 'Ctrl+`',
      action: () => console.log('Toggle Terminal'),
    },
    {
      id: 'problems',
      label: 'Problems',
      icon: null,
      shortcut: 'Ctrl+Shift+M',
      action: () => console.log('Toggle Problems'),
    },
    {
      id: 'output',
      label: 'Output',
      icon: null,
      shortcut: 'Ctrl+Shift+U',
      action: () => console.log('Toggle Output'),
    },
    {
      id: 'debug-console',
      label: 'Debug Console',
      icon: null,
      shortcut: 'Ctrl+Shift+Y',
      action: () => console.log('Toggle Debug Console'),
    },
    { type: 'separator' },
    {
      id: 'sidebar',
      label: 'Toggle Sidebar',
      icon: Sidebar,
      shortcut: 'Ctrl+B',
      action: () => console.log('Toggle Sidebar'),
    },
    {
      id: 'ai-panel',
      label: 'Toggle AI Panel',
      icon: null,
      shortcut: 'Ctrl+Shift+A',
      action: () => console.log('Toggle AI Panel'),
    },
    { type: 'separator' },
    {
      id: 'layout',
      label: 'Editor Layout',
      icon: Layout,
      shortcut: '',
      submenu: [
        { label: 'Single', action: () => console.log('Single Layout') },
        { label: 'Two Columns', action: () => console.log('Two Columns') },
        { label: 'Three Columns', action: () => console.log('Three Columns') },
        { label: 'Two Rows', action: () => console.log('Two Rows') },
        { label: 'Grid (2x2)', action: () => console.log('Grid Layout') },
      ],
    },
    { type: 'separator' },
    {
      id: 'zoom-in',
      label: 'Zoom In',
      icon: null,
      shortcut: 'Ctrl+=',
      action: () => console.log('Zoom In'),
    },
    {
      id: 'zoom-out',
      label: 'Zoom Out',
      icon: null,
      shortcut: 'Ctrl+-',
      action: () => console.log('Zoom Out'),
    },
    {
      id: 'reset-zoom',
      label: 'Reset Zoom',
      icon: null,
      shortcut: 'Ctrl+0',
      action: () => console.log('Reset Zoom'),
    },
    { type: 'separator' },
    {
      id: 'fullscreen',
      label: 'Toggle Full Screen',
      icon: Maximize,
      shortcut: 'F11',
      action: () => console.log('Toggle Fullscreen'),
    },
  ];

  const handleItemClick = (item: any) => {
    if (item.action) {
      item.action();
      onClose();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.1 }}
      className="bg-popover border border-border rounded-md shadow-lg py-1 min-w-[220px] z-50"
      onMouseLeave={onClose}
    >
      {menuItems.map((item, index) => {
        if (item.type === 'separator') {
          return <div key={index} className="h-px bg-border my-1" />;
        }

        return (
          <button
            key={item.id}
            onClick={() => handleItemClick(item)}
            className="w-full flex items-center justify-between px-3 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground transition-colors text-left"
          >
            <div className="flex items-center space-x-2">
              {item.icon && <item.icon size={16} />}
              <span>{item.label}</span>
            </div>
            {item.shortcut && (
              <span className="text-xs text-muted-foreground">{item.shortcut}</span>
            )}
          </button>
        );
      })}
    </motion.div>
  );
}
