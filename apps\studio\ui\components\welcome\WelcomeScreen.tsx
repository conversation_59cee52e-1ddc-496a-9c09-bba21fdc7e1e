import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FolderOpen, 
  GitBranch, 
  Plus, 
  Clock, 
  Star,
  Download,
  Zap,
  Code,
  Palette,
  Users,
  BookOpen,
  Settings
} from 'lucide-react';

interface WelcomeScreenProps {
  onWorkspaceSelected: () => void;
}

export default function WelcomeScreen({ onWorkspaceSelected }: WelcomeScreenProps) {
  const [recentProjects] = useState([
    { name: 'my-react-app', path: '/Users/<USER>/my-react-app', lastOpened: '2 hours ago' },
    { name: 'kodekilat-extension', path: '/Users/<USER>/kodekilat-extension', lastOpened: '1 day ago' },
    { name: 'portfolio-website', path: '/Users/<USER>/portfolio-website', lastOpened: '3 days ago' }
  ]);

  const handleOpenFolder = () => {
    // In real app, this would open file dialog
    console.log('Opening folder dialog...');
    onWorkspaceSelected();
  };

  const handleCloneRepo = () => {
    // In real app, this would open clone dialog
    console.log('Opening clone dialog...');
  };

  const handleNewProject = () => {
    // In real app, this would open new project wizard
    console.log('Opening new project wizard...');
    onWorkspaceSelected();
  };

  const handleOpenRecent = (project: any) => {
    console.log('Opening recent project:', project.name);
    onWorkspaceSelected();
  };

  return (
    <div className="h-screen bg-background flex">
      {/* Left Panel - Logo & Branding */}
      <div className="w-1/2 flex flex-col items-center justify-center bg-gradient-to-br from-primary/10 to-yellow-500/10 p-8">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          {/* Logo */}
          <div className="mb-8">
            <svg
              width="120"
              height="120"
              viewBox="0 0 80 80"
              className="mx-auto drop-shadow-2xl"
            >
              <path
                d="M30 20 L50 35 L45 35 L60 60 L40 45 L45 45 Z"
                fill="#FACC15"
                className="drop-shadow-lg"
              />
            </svg>
          </div>

          {/* Title */}
          <h1 className="text-4xl font-bold text-foreground mb-4">
            KodeKilat Studio ⚡
          </h1>
          
          {/* Tagline */}
          <p className="text-xl text-muted-foreground mb-8 max-w-md">
            IDE Nusantara Modern. Real-Time. Cerdas. Tanpa Dummy.
          </p>

          {/* Features */}
          <div className="grid grid-cols-2 gap-4 max-w-md">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Zap size={16} className="text-yellow-500" />
              <span>KodeKilat AI</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Code size={16} className="text-blue-500" />
              <span>Smart Editor</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Palette size={16} className="text-purple-500" />
              <span>UI Designer</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Users size={16} className="text-green-500" />
              <span>Live Collaboration</span>
            </div>
          </div>

          {/* Version */}
          <div className="mt-8 text-xs text-muted-foreground">
            Version 1.0.0 Beta • Made with ❤️ in Indonesia
          </div>
        </motion.div>
      </div>

      {/* Right Panel - Actions */}
      <div className="w-1/2 p-8 flex flex-col">
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex-1"
        >
          {/* Header */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-2">
              Selamat Datang
            </h2>
            <p className="text-muted-foreground">
              Mulai coding dengan IDE Nusantara yang powerful
            </p>
          </div>

          {/* Quick Actions */}
          <div className="space-y-4 mb-8">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleOpenFolder}
              className="w-full flex items-center space-x-4 p-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              <FolderOpen size={24} />
              <div className="text-left">
                <div className="font-medium">Buka Folder</div>
                <div className="text-sm opacity-90">Buka folder project yang sudah ada</div>
              </div>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleCloneRepo}
              className="w-full flex items-center space-x-4 p-4 bg-accent text-accent-foreground rounded-lg hover:bg-accent/80 transition-colors"
            >
              <GitBranch size={24} />
              <div className="text-left">
                <div className="font-medium">Clone Repository</div>
                <div className="text-sm opacity-75">Clone project dari GitHub</div>
              </div>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleNewProject}
              className="w-full flex items-center space-x-4 p-4 bg-accent text-accent-foreground rounded-lg hover:bg-accent/80 transition-colors"
            >
              <Plus size={24} />
              <div className="text-left">
                <div className="font-medium">Project Baru</div>
                <div className="text-sm opacity-75">Mulai project dari template</div>
              </div>
            </motion.button>
          </div>

          {/* Recent Projects */}
          {recentProjects.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-medium text-foreground mb-4 flex items-center">
                <Clock size={20} className="mr-2" />
                Project Terbaru
              </h3>
              <div className="space-y-2">
                {recentProjects.map((project, index) => (
                  <motion.button
                    key={project.path}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 * index }}
                    onClick={() => handleOpenRecent(project)}
                    className="w-full flex items-center justify-between p-3 bg-secondary hover:bg-accent rounded-lg transition-colors text-left"
                  >
                    <div className="flex items-center space-x-3">
                      <FolderOpen size={16} className="text-blue-400" />
                      <div>
                        <div className="font-medium text-sm">{project.name}</div>
                        <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                          {project.path}
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {project.lastOpened}
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>
          )}

          {/* Quick Links */}
          <div className="grid grid-cols-2 gap-4">
            <button className="flex items-center space-x-2 p-3 bg-secondary hover:bg-accent rounded-lg transition-colors text-sm">
              <BookOpen size={16} />
              <span>Dokumentasi</span>
            </button>
            <button className="flex items-center space-x-2 p-3 bg-secondary hover:bg-accent rounded-lg transition-colors text-sm">
              <Download size={16} />
              <span>Extensions</span>
            </button>
            <button className="flex items-center space-x-2 p-3 bg-secondary hover:bg-accent rounded-lg transition-colors text-sm">
              <Star size={16} />
              <span>Community</span>
            </button>
            <button className="flex items-center space-x-2 p-3 bg-secondary hover:bg-accent rounded-lg transition-colors text-sm">
              <Settings size={16} />
              <span>Settings</span>
            </button>
          </div>
        </motion.div>

        {/* Footer */}
        <div className="text-center text-xs text-muted-foreground">
          <p>© 2024 KodeKilat Studio. Open Source IDE untuk Developer Indonesia.</p>
        </div>
      </div>
    </div>
  );
}
