{"name": "@kodekilat/studio-ui", "version": "1.0.0", "description": "KodeKilat Studio - Next.js Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export", "clean": "rimraf .next out"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@kodekilat/extensions": "workspace:*", "@kodekilat/ui-designer": "workspace:*", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "postcss": "^8.4.32", "rimraf": "^5.0.5", "tailwindcss": "^3.3.6", "typescript": "^5.3.0"}}