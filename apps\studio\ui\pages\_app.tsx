import '@/styles/globals.css';
import type { AppProps } from 'next/app';
import { useEffect } from 'react';

export default function App({ Component, pageProps }: AppProps) {
  useEffect(() => {
    // Set up global keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // Command Palette
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        // TODO: Open command palette
        console.log('Command Palette triggered');
      }

      // Quick Open
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        // TODO: Open quick file picker
        console.log('Quick Open triggered');
      }

      // Save
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        // TODO: Save current file
        console.log('Save triggered');
      }

      // New File
      if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        // TODO: Create new file
        console.log('New File triggered');
      }

      // Toggle Sidebar
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        // TODO: Toggle sidebar
        console.log('Toggle Sidebar triggered');
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    // <RemoteCollaborationProvider enabled={true}>
    <Component {...pageProps} />
    // </RemoteCollaborationProvider>
  );
}
