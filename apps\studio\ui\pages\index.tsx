import MainLayout from '@/components/layout/MainLayout';
import WelcomeScreen from '@/components/welcome/WelcomeScreen';
import { motion } from 'framer-motion';
import Head from 'next/head';
import { useEffect, useState } from 'react';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);
  const [hasWorkspace, setHasWorkspace] = useState(false);

  useEffect(() => {
    // Simulate loading and check for existing workspace
    const checkWorkspace = async () => {
      try {
        // Check if we have a workspace from electron store
        if (typeof window !== 'undefined' && window.electronAPI) {
          const lastWorkspace = await window.electronAPI.store.get('lastWorkspace');
          setHasWorkspace(!!lastWorkspace);
        }
      } catch (error) {
        console.error('Error checking workspace:', error);
      } finally {
        setTimeout(() => setIsLoading(false), 1000); // Minimum loading time
      }
    };

    checkWorkspace();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="mb-4">
            <svg
              width="80"
              height="80"
              viewBox="0 0 80 80"
              className="mx-auto animate-pulse-glow"
            >
              <path
                d="M30 20 L50 35 L45 35 L60 60 L40 45 L45 45 Z"
                fill="#FACC15"
                className="drop-shadow-lg"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-primary mb-2">
            KodeKilat Studio ⚡
          </h1>
          <p className="text-muted-foreground">Loading IDE Nusantara...</p>
          <div className="flex justify-center mt-4 space-x-1 loading-dots">
            <span className="w-2 h-2 bg-primary rounded-full"></span>
            <span className="w-2 h-2 bg-primary rounded-full"></span>
            <span className="w-2 h-2 bg-primary rounded-full"></span>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>KodeKilat Studio ⚡ - IDE Nusantara Modern</title>
      </Head>

      {hasWorkspace ? (
        <MainLayout />
      ) : (
        <WelcomeScreen onWorkspaceSelected={() => setHasWorkspace(true)} />
      )}
    </>
  );
}
