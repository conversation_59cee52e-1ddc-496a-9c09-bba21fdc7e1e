import { motion } from 'framer-motion';
import Head from 'next/head';

export default function Splash() {
  return (
    <>
      <Head>
        <title>KodeKilat Studio ⚡ - Loading</title>
      </Head>
      
      <div className="flex items-center justify-center h-screen bg-gradient-to-br from-background to-background/80">
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center"
        >
          {/* Lightning Logo */}
          <motion.div
            initial={{ y: -20 }}
            animate={{ y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="mb-8"
          >
            <svg
              width="120"
              height="120"
              viewBox="0 0 120 120"
              className="mx-auto"
            >
              <defs>
                <linearGradient id="lightningGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#FACC15" />
                  <stop offset="50%" stopColor="#FDE047" />
                  <stop offset="100%" stopColor="#EAB308" />
                </linearGradient>
                <filter id="glowEffect">
                  <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                  <feMerge> 
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>
              
              <motion.path
                d="M45 30 L75 52.5 L67.5 52.5 L90 90 L60 67.5 L67.5 67.5 Z"
                fill="url(#lightningGrad)"
                filter="url(#glowEffect)"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ delay: 0.5, duration: 1.2, ease: "easeInOut" }}
              />
            </svg>
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="text-4xl font-bold text-primary mb-3"
          >
            KodeKilat Studio
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0, duration: 0.6 }}
            className="text-lg text-muted-foreground mb-8"
          >
            IDE Nusantara Modern
          </motion.p>

          {/* Loading Animation */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2, duration: 0.4 }}
            className="flex justify-center space-x-2"
          >
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-3 h-3 bg-primary rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </motion.div>

          {/* Version */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5, duration: 0.4 }}
            className="text-xs text-muted-foreground mt-8"
          >
            Version 1.0.0 - Beta
          </motion.p>
        </motion.div>
      </div>
    </>
  );
}
