import { motion } from 'framer-motion';
import Head from 'next/head';
import { useCallback, useState } from 'react';
// import { U<PERSON>esigner } from '@kodekilat/ui-designer';
// import { UIElement } from '@kodekilat/ui-designer';

interface UIDesignerPageProps {}

export default function UIDesignerPage({}: UIDesignerPageProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Handle save design
  const handleSave = useCallback(async (design: any[]) => {
    setIsLoading(true);
    try {
      // Save to localStorage for now
      localStorage.setItem(
        'kodekilat-ui-design',
        JSON.stringify({
          design,
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        })
      );

      setLastSaved(new Date());

      // TODO: Integrate with backend API
      // await fetch('/api/ui-designer/save', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ design })
      // });

      console.log('✅ Design saved successfully:', design);
    } catch (error) {
      console.error('❌ Failed to save design:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle load design
  const handleLoad = useCallback(async (): Promise<any[]> => {
    try {
      // Load from localStorage for now
      const saved = localStorage.getItem('kodekilat-ui-design');
      if (saved) {
        const data = JSON.parse(saved);
        console.log('✅ Design loaded successfully:', data.design);
        return data.design || [];
      }

      // TODO: Integrate with backend API
      // const response = await fetch('/api/ui-designer/load');
      // const data = await response.json();
      // return data.design || [];

      return [];
    } catch (error) {
      console.error('❌ Failed to load design:', error);
      return [];
    }
  }, []);

  return (
    <>
      <Head>
        <title>UI Designer - KodeKilat Studio ⚡</title>
        <meta
          name="description"
          content="Visual UI Designer untuk membangun interface dengan drag & drop"
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="h-screen bg-background text-foreground overflow-hidden">
        {/* Loading Overlay */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
          >
            <div className="flex items-center space-x-3 bg-background border border-border rounded-lg p-6 shadow-lg">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span className="text-sm font-medium">Saving design...</span>
            </div>
          </motion.div>
        )}

        {/* Status Bar */}
        <div className="h-8 bg-secondary/50 border-b border-border flex items-center justify-between px-4 text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span className="font-medium text-foreground">UI Designer</span>
            <span>•</span>
            <span>Visual interface builder dengan drag & drop</span>
          </div>
          <div className="flex items-center space-x-4">
            {lastSaved && (
              <>
                <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
                <span>•</span>
              </>
            )}
            <span>KodeKilat Studio ⚡</span>
          </div>
        </div>

        {/* UI Designer */}
        <div className="h-[calc(100vh-2rem)]">
          {/* <UIDesigner className="w-full h-full" onSave={handleSave} onLoad={handleLoad} /> */}
          <div className="w-full h-full flex items-center justify-center bg-muted/20">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">UI Designer</h2>
              <p className="text-muted-foreground">Visual UI builder coming soon ⚡</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Static props for better performance
export async function getStaticProps() {
  return {
    props: {},
  };
}
