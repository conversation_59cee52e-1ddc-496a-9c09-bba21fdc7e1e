import { motion } from 'framer-motion';
import { Download, FileText, FolderOpen, GitBranch, Github, Plus, Zap } from 'lucide-react';
import Head from 'next/head';
import { useState } from 'react';

interface WelcomeProps {
  onWorkspaceSelected?: () => void;
}

export default function Welcome({ onWorkspaceSelected }: WelcomeProps) {
  const [recentProjects] = useState([
    { name: 'my-react-app', path: '/Users/<USER>/my-react-app', lastOpened: '2 hours ago' },
    {
      name: 'kodekilat-extension',
      path: '/Users/<USER>/kodekilat-extension',
      lastOpened: '1 day ago',
    },
    { name: 'portfolio-website', path: '/Users/<USER>/portfolio-website', lastOpened: '3 days ago' },
  ]);

  const handleOpenFolder = async () => {
    try {
      if ((window as any).electronAPI) {
        const result = await (window as any).electronAPI.openFolder();
        if (!result.canceled && result.filePaths.length > 0) {
          await (window as any).electronAPI.store.set('lastWorkspace', result.filePaths[0]);
          onWorkspaceSelected?.();
        }
      }
    } catch (error) {
      console.error('Error opening folder:', error);
    }
  };

  const handleCloneRepository = () => {
    // TODO: Implement git clone dialog
    console.log('Clone repository');
  };

  const handleNewProject = () => {
    // TODO: Implement new project wizard
    console.log('New project');
  };

  const handleOpenRecent = async (projectPath: string) => {
    try {
      if ((window as any).electronAPI) {
        await (window as any).electronAPI.store.set('lastWorkspace', projectPath);
        onWorkspaceSelected?.();
      }
    } catch (error) {
      console.error('Error opening recent project:', error);
    }
  };

  return (
    <>
      <Head>
        <title>Welcome - KodeKilat Studio ⚡</title>
      </Head>

      <div className="h-screen bg-background flex">
        {/* Left Panel - Logo & Actions */}
        <div className="w-1/2 flex flex-col justify-center items-center p-12 bg-gradient-to-br from-background to-muted/20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <div className="mb-6">
              <Zap className="w-20 h-20 text-primary mx-auto mb-4" />
            </div>
            <h1 className="text-4xl font-bold text-primary mb-2">KodeKilat Studio</h1>
            <p className="text-lg text-muted-foreground">IDE Nusantara Lokal. Real-Time. Cerdas.</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-4 w-full max-w-sm"
          >
            <button
              onClick={handleOpenFolder}
              className="w-full flex items-center justify-center space-x-3 p-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              <FolderOpen className="w-5 h-5" />
              <span>Open Folder</span>
            </button>

            <button
              onClick={handleCloneRepository}
              className="w-full flex items-center justify-center space-x-3 p-4 bg-muted text-foreground rounded-lg hover:bg-accent transition-colors"
            >
              <GitBranch className="w-5 h-5" />
              <span>Clone Repository</span>
            </button>

            <button
              onClick={handleNewProject}
              className="w-full flex items-center justify-center space-x-3 p-4 bg-muted text-foreground rounded-lg hover:bg-accent transition-colors"
            >
              <Plus className="w-5 h-5" />
              <span>New Project</span>
            </button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-12 text-center"
          >
            <p className="text-sm text-muted-foreground mb-4">Quick Links</p>
            <div className="flex space-x-4">
              <button className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors">
                <FileText className="w-4 h-4" />
                <span>Documentation</span>
              </button>
              <button className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors">
                <Github className="w-4 h-4" />
                <span>GitHub</span>
              </button>
              <button className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors">
                <Download className="w-4 h-4" />
                <span>Extensions</span>
              </button>
            </div>
          </motion.div>
        </div>

        {/* Right Panel - Recent Projects */}
        <div className="w-1/2 p-12 border-l border-border">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-2xl font-semibold mb-6">Recent Projects</h2>

            {recentProjects.length > 0 ? (
              <div className="space-y-3">
                {recentProjects.map((project, index) => (
                  <motion.button
                    key={project.path}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                    onClick={() => handleOpenRecent(project.path)}
                    className="w-full text-left p-4 rounded-lg border border-border hover:bg-accent transition-colors group"
                  >
                    <div className="flex items-center space-x-3">
                      <FolderOpen className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
                      <div className="flex-1">
                        <h3 className="font-medium">{project.name}</h3>
                        <p className="text-sm text-muted-foreground truncate">{project.path}</p>
                        <p className="text-xs text-muted-foreground">{project.lastOpened}</p>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <FolderOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No recent projects</p>
                <p className="text-sm text-muted-foreground">Open a folder to get started</p>
              </div>
            )}

            <div className="mt-8 pt-6 border-t border-border">
              <h3 className="text-lg font-medium mb-4">Getting Started</h3>
              <div className="space-y-2 text-sm">
                <p className="text-muted-foreground">
                  • Press <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+Shift+P</kbd> to
                  open Command Palette
                </p>
                <p className="text-muted-foreground">
                  • Press <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+P</kbd> for Quick
                  Open
                </p>
                <p className="text-muted-foreground">
                  • Press <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+B</kbd> to toggle
                  Sidebar
                </p>
                <p className="text-muted-foreground">
                  • Use KodeKilat AI in the right sidebar for assistance
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </>
  );
}
