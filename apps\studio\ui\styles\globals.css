@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 30 30 30; /* #1e1e1e */
    --foreground: 0 0% 100%; /* #ffffff */
    --card: 45 45 45; /* #2d2d2d */
    --card-foreground: 0 0% 100%;
    --popover: 45 45 45; /* #2d2d2d */
    --popover-foreground: 0 0% 100%;
    --primary: 45 93% 53%; /* #FACC15 */
    --primary-foreground: 30 30 30;
    --secondary: 62 62 62; /* #3e3e3e */
    --secondary-foreground: 0 0% 100%;
    --muted: 45 45 45; /* #2d2d2d */
    --muted-foreground: 0 0% 61%; /* #9ca3af */
    --accent: 62 62 62; /* #3e3e3e */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%; /* #ef4444 */
    --destructive-foreground: 0 0% 100%;
    --border: 64 64 64; /* #404040 */
    --input: 62 62 62; /* #3e3e3e */
    --ring: 45 93% 53%; /* #FACC15 */
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html, body, #__next {
    height: 100%;
    overflow: hidden;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a4a4a;
}

/* Monaco Editor custom styles */
.monaco-editor {
  background-color: #1e1e1e !important;
}

.monaco-editor .margin {
  background-color: #1e1e1e !important;
}

/* Custom glow effects */
.glow-primary {
  box-shadow: 0 0 10px rgba(250, 204, 21, 0.3);
}

.glow-primary:hover {
  box-shadow: 0 0 20px rgba(250, 204, 21, 0.5);
}

/* Terminal styles */
.xterm {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

.xterm .xterm-viewport {
  background-color: #1e1e1e !important;
}

/* File tree styles */
.file-tree-item {
  @apply flex items-center px-2 py-1 text-sm hover:bg-accent rounded-sm cursor-pointer;
}

.file-tree-item.selected {
  @apply bg-primary/20 text-primary;
}

.file-tree-item.modified {
  @apply text-yellow-400;
}

.file-tree-item.new {
  @apply text-green-400;
}

.file-tree-item.deleted {
  @apply text-red-400;
}

/* Tab styles */
.editor-tab {
  @apply flex items-center px-3 py-2 text-sm bg-muted border-r border-border hover:bg-accent;
}

.editor-tab.active {
  @apply bg-background text-primary;
}

.editor-tab.modified::after {
  content: "●";
  @apply ml-2 text-yellow-400;
}

/* Status bar styles */
.status-bar-item {
  @apply flex items-center px-2 py-1 text-xs hover:bg-accent cursor-pointer;
}

/* Command palette styles */
.command-palette {
  @apply fixed top-1/4 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 max-w-full;
  @apply bg-popover border border-border rounded-lg shadow-2xl z-50;
}

/* AI chat styles */
.ai-message {
  @apply mb-4 p-3 rounded-lg;
}

.ai-message.user {
  @apply bg-primary/10 ml-8;
}

.ai-message.assistant {
  @apply bg-muted mr-8;
}

/* Loading animations */
@keyframes pulse-dot {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-dots span {
  animation: pulse-dot 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

/* Drag and drop styles */
.drag-over {
  @apply border-2 border-dashed border-primary bg-primary/5;
}

/* Resizable panels */
.resize-handle {
  @apply bg-border hover:bg-primary/50 transition-colors;
}

.resize-handle:hover {
  @apply bg-primary/30;
}

/* Context menu */
.context-menu {
  @apply bg-popover border border-border rounded-md shadow-lg py-1 z-50;
}

.context-menu-item {
  @apply px-3 py-2 text-sm hover:bg-accent cursor-pointer flex items-center;
}

.context-menu-separator {
  @apply h-px bg-border my-1;
}
