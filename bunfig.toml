# Bun configuration for KodeKilat Studio
entrypoint = "apps/studio/main.ts"

[install]
# Configure package installation
cache = true
exact = false
production = false
optional = true
dev = true
peer = true

# Registry configuration
registry = "https://registry.npmjs.org/"
scopes = {}

# Lockfile configuration
lockfile = true

[install.cache]
# Cache configuration
dir = "~/.bun/install/cache"
disable = false
disableManifest = false

[run]
# Runtime configuration
bun = true
silent = false

[test]
# Test configuration
preload = []
smol = false
coverage = false
bail = 0

[build]
# Build configuration
target = "bun"
outdir = "./dist"
splitting = true
minify = false
sourcemap = "external"

# Define macros for development
[define]
"process.env.NODE_ENV" = "development"
"process.env.KODEKILAT_VERSION" = "1.0.0"

# Development server configuration
[dev]
port = 3001
host = "localhost"
hot = true

# Workspace configuration
[workspace]
# Enable workspace support for monorepo
enabled = true

# Package manager settings
[pm]
# Use Bun as package manager
defaultRegistry = "https://registry.npmjs.org/"
cache = true
