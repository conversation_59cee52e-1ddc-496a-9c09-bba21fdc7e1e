# 🛠️ Development Guide - KodeKilat Studio

## 🚀 Quick Start

### Prerequisites
- **Bun** >= 1.0.0
- **Node.js** >= 18.0.0
- **Git** for version control

### Installation
```bash
# Clone repository
git clone https://github.com/kangpcode/kodekilat-studio.git
cd kodekilat-studio

# Install dependencies
bun install

# Setup development environment
bun run prepare
```

### Development Commands

#### 🏃‍♂️ Start Development
```bash
# Start all services (API + UI + Electron)
bun run dev

# Start individual services
bun run dev:api      # API Server only
bun run dev:ui       # Next.js UI only
bun run dev:electron # Electron only
```

#### 🔧 Build & Test
```bash
# Build all packages
bun run build

# Build for production
bun run build:prod

# Run tests
bun test
bun run test:watch
bun run test:coverage

# Type checking
bun run type-check
bun run type-check:watch
```

#### 🧹 Code Quality
```bash
# Lint code
bun run lint
bun run lint:fix

# Format code
bun run format
bun run format:check

# Clean build artifacts
bun run clean
bun run clean:all
```

## 📁 Project Structure

```
kodekilat-studio/
├── apps/
│   ├── api-server/          # Bun API backend
│   ├── studio/              # Electron main process
│   └── studio-ui/           # Next.js frontend
├── packages/
│   ├── editor/              # Monaco editor package
│   ├── terminal/            # XTerm.js terminal package
│   ├── ai/                  # AI assistant package
│   └── filesystem/          # File system package
├── shared/
│   ├── types/               # TypeScript definitions
│   └── schemas/             # JSON schemas
├── .vscode/                 # VSCode configuration
├── docs/                    # Documentation
└── dist/                    # Build output
```

## 🔧 Development Tools

### VSCode Configuration
- **Settings**: Optimized for TypeScript, ESLint, Prettier
- **Tasks**: Pre-configured build and development tasks
- **Launch**: Debug configurations for all components
- **Extensions**: Recommended extensions for optimal DX

### Code Quality Tools
- **ESLint**: TypeScript, React, accessibility rules
- **Prettier**: Consistent code formatting
- **Husky**: Git hooks for pre-commit checks
- **lint-staged**: Run linters on staged files only

### Testing Framework
- **Jest**: Unit and integration testing
- **@testing-library**: React component testing
- **Coverage**: Minimum 70% coverage threshold

## 🎯 Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/new-feature

# Start development servers
bun run dev

# Make changes and test
bun run lint
bun run test
bun run type-check
```

### 2. Code Quality Checks
Pre-commit hooks automatically run:
- TypeScript type checking
- ESLint linting with auto-fix
- Prettier formatting
- Staged files only

### 3. Testing
```bash
# Run all tests
bun test

# Watch mode for development
bun run test:watch

# Coverage report
bun run test:coverage
```

### 4. Building
```bash
# Development build
bun run build

# Production build
bun run build:prod

# Electron distribution
bun run dist
```

## 🏗️ Architecture

### Monorepo Structure
- **Non-modular**: Feature-based organization
- **Workspaces**: Bun workspaces for dependency management
- **Shared Types**: Centralized TypeScript definitions

### Technology Stack
- **Frontend**: Next.js 14, React 18, TailwindCSS, shadcn/ui
- **Backend**: Bun runtime, SQLite database, WebSocket
- **Desktop**: Electron with secure IPC
- **Editor**: Monaco Editor with custom themes
- **Terminal**: XTerm.js with addons

### Communication
- **IPC**: Electron main ↔ renderer
- **HTTP**: Frontend ↔ API server
- **WebSocket**: Real-time collaboration
- **Events**: Custom event system

## 🔍 Debugging

### VSCode Debug Configurations
- **Electron Main**: Debug main process
- **API Server**: Debug Bun backend
- **Next.js UI**: Debug frontend
- **Full Stack**: Debug all components

### Debug Commands
```bash
# Debug with inspect
bun --inspect apps/api-server/index.ts

# Electron debug mode
NODE_ENV=development bun run dev:electron
```

## 📦 Package Management

### Adding Dependencies
```bash
# Root dependencies
bun add package-name

# Workspace dependencies
bun add package-name --workspace apps/api-server

# Development dependencies
bun add -d package-name
```

### Package Scripts
Each package has standardized scripts:
- `build`: TypeScript compilation
- `dev`: Watch mode development
- `lint`: ESLint checking
- `format`: Prettier formatting
- `type-check`: TypeScript validation

## 🚀 Deployment

### Build Process
1. **Type Check**: Validate TypeScript
2. **Lint**: Check code quality
3. **Test**: Run test suite
4. **Build**: Compile all packages
5. **Package**: Create Electron distributables

### Distribution
```bash
# All platforms
bun run dist

# Specific platforms
bun run dist:win
bun run dist:mac
bun run dist:linux
```

## 🤝 Contributing

### Code Style
- **TypeScript**: Strict mode enabled
- **React**: Functional components with hooks
- **Naming**: camelCase for variables, PascalCase for components
- **Files**: kebab-case for file names

### Commit Convention
```bash
feat: add new feature
fix: bug fix
docs: documentation update
style: formatting changes
refactor: code refactoring
test: add tests
chore: maintenance tasks
```

### Pull Request Process
1. Create feature branch
2. Make changes with tests
3. Ensure all checks pass
4. Submit PR with description
5. Code review and merge

## 🆘 Troubleshooting

### Common Issues
- **Port conflicts**: Change ports in environment files
- **Build failures**: Clear cache with `bun run clean`
- **Type errors**: Run `bun run type-check` for details
- **Dependency issues**: Delete node_modules and reinstall

### Performance Tips
- Use `bun run dev` for fastest development
- Enable TypeScript incremental compilation
- Use VSCode workspace for better IntelliSense
- Monitor memory usage in Electron DevTools

---

**Happy Coding! ⚡**
