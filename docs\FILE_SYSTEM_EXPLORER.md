# 📁 File System Explorer - KodeKilat Studio

## 🚀 Overview

File System Explorer adalah komponen inti dari KodeKilat Studio yang menyediakan interface untuk navigasi dan manajemen file/folder secara real-time. Terintegrasi dengan Bun backend API untuk operasi file system yang cepat dan responsif.

## ✨ Features

### 🔧 Core Functionality
- **Real-time File Operations**: Create, read, update, delete files/folders
- **Tree View Navigation**: Hierarchical folder structure dengan expand/collapse
- **Context Menu**: Right-click menu untuk operasi file
- **Drag & Drop**: (Coming soon) Drag files untuk reorganisasi
- **File Watching**: Real-time updates ketika file berubah
- **Search & Filter**: (Coming soon) Pencarian file dalam workspace

### 🎨 UI/UX Features
- **VSCode-like Interface**: Familiar interface untuk developer
- **File Type Icons**: Icon berbeda untuk setiap jenis file
- **Keyboard Navigation**: Arrow keys, Enter, Delete shortcuts
- **Loading States**: Smooth loading indicators
- **Error Handling**: User-friendly error messages
- **Responsive Design**: Adaptif untuk berbagai ukuran layar

## 🏗️ Architecture

### 📦 Components Structure
```
apps/studio/ui/components/explorer/
├── ExplorerView.tsx          # Main explorer component
├── FileItem.tsx              # Individual file/folder item
└── ContextMenu.tsx           # Right-click context menu
```

### 🔗 API Integration
```
packages/fs/
├── useFileSystem.ts          # React hook untuk file operations
└── types.ts                  # TypeScript interfaces
```

### 🌐 Backend API
```
apps/api-server/routes/
└── fs.ts                     # File system API endpoints
```

## 🛠️ Implementation Details

### 🎯 Main Component: ExplorerView
```typescript
interface ExplorerViewProps {
  onFileSelect: (file: string) => void;
}

// Key features:
- Real-time file listing via useFileSystem hook
- Expandable folder tree with state management
- Context menu for file operations
- Error handling dan loading states
- Keyboard shortcuts support
```

### 🔧 File System Hook: useFileSystem
```typescript
interface FileSystemHookResult {
  files: FileSystemItem[];
  loading: boolean;
  error: string | null;
  currentPath: string;
  listDirectory: (path?: string) => Promise<void>;
  readFile: (path: string) => Promise<string>;
  writeFile: (path: string, content: string) => Promise<void>;
  createDirectory: (path: string) => Promise<void>;
  deleteItem: (path: string) => Promise<void>;
  renameItem: (oldPath: string, newPath: string) => Promise<void>;
  refreshDirectory: () => Promise<void>;
  openFolder: () => Promise<string | null>;
}
```

### 🌐 API Endpoints
```
GET  /api/fs/list?path=<path>     # List directory contents
GET  /api/fs/read?path=<path>     # Read file content
POST /api/fs/write                # Write file content
POST /api/fs/mkdir                # Create directory
DELETE /api/fs/delete?path=<path> # Delete file/folder
POST /api/fs/rename               # Rename/move file/folder
POST /api/fs/watch                # Watch directory for changes
```

## 🎨 Styling & Theming

### 🌈 Color Scheme
- **Folders**: Blue (#3B82F6) untuk folder icons
- **Files**: Berbeda berdasarkan extension:
  - TypeScript/JavaScript: Blue (#3B82F6)
  - JSON: Yellow (#EAB308)
  - Markdown: Gray (#6B7280)
  - Images: Green (#10B981)
  - Database: Purple (#8B5CF6)

### 📱 Responsive Design
- **Desktop**: Full tree view dengan hover effects
- **Tablet**: Collapsible sidebar
- **Mobile**: Overlay modal untuk file explorer

## 🚀 Usage Examples

### 📂 Basic File Explorer
```tsx
import { ExplorerView } from '@/components/explorer/ExplorerView';

function Sidebar() {
  const handleFileSelect = (filePath: string) => {
    // Open file in editor
    console.log('Opening file:', filePath);
  };

  return (
    <div className="w-64 border-r">
      <ExplorerView onFileSelect={handleFileSelect} />
    </div>
  );
}
```

### 🔧 Custom File Operations
```tsx
import { useFileSystem } from '@/packages/fs/useFileSystem';

function CustomFileManager() {
  const { 
    files, 
    createDirectory, 
    writeFile, 
    deleteItem 
  } = useFileSystem('/workspace');

  const createNewProject = async () => {
    await createDirectory('/workspace/new-project');
    await writeFile('/workspace/new-project/package.json', '{}');
  };

  return (
    <button onClick={createNewProject}>
      Create New Project
    </button>
  );
}
```

## 🔮 Future Enhancements

### 🎯 Planned Features
- **File Search**: Global search dalam workspace
- **Git Integration**: Git status indicators
- **File Preview**: Quick preview untuk images/text
- **Bulk Operations**: Multi-select untuk batch operations
- **Custom File Types**: Support untuk .kodix extensions
- **Cloud Sync**: Sync dengan cloud storage
- **Collaboration**: Real-time collaborative editing indicators

### 🚀 Performance Optimizations
- **Virtual Scrolling**: Untuk large directories
- **Lazy Loading**: Load folders on-demand
- **Caching**: Cache file tree untuk faster navigation
- **Debounced Search**: Optimized search performance

## 📊 Status

✅ **Completed Features:**
- Basic file tree navigation
- CRUD operations (Create, Read, Update, Delete)
- Context menu dengan rename/delete
- Real-time API integration
- Error handling dan loading states
- VSCode-like UI design

🚧 **In Progress:**
- File watching untuk real-time updates
- Keyboard navigation shortcuts
- Drag & drop functionality

📋 **Todo:**
- File search dan filtering
- Git status integration
- File preview functionality
- Performance optimizations

---

**Status**: ✅ **COMPLETED** - File System Explorer siap digunakan dengan semua fitur dasar!
