# 🤖 KodeKilat AI Integration

## Overview

KodeKilat AI adalah sistem AI assistant yang terintegrasi penuh untuk menggantikan GitHub Copilot dengan dukungan multiple provider dan fitur-fitur canggih untuk coding assistance.

## ✨ Features

### 🔥 Core Features
- **Multiple AI Providers**: OpenRouter, HuggingFace, Ollama, OpenAI
- **Real-time Chat**: Interactive AI conversation dengan context awareness
- **Code Completion**: Auto-completion dengan context dari file aktif
- **Code Explanation**: Penjelasan kode yang dipilih
- **Code Generation**: Generate kode berdasarkan deskripsi
- **Code Refactoring**: Refactor kode dengan saran AI
- **Settings Management**: Konfigurasi provider dan preferensi AI

### 🎯 Advanced Features
- **Context Awareness**: AI memahami file aktif dan selected text
- **Provider Management**: Add, update, remove, dan switch provider
- **Settings Persistence**: Konfigurasi tersimpan di localStorage
- **Error Handling**: Comprehensive error handling dan fallback
- **TypeScript Support**: Full type safety untuk semua operasi

## 🏗️ Architecture

### Core Components

#### 1. `useKodeKilatAI` Hook
Hook utama yang menyediakan semua functionality AI:

```typescript
const {
  messages,
  isLoading,
  error,
  sendMessage,
  clearMessages,
  providers,
  activeProvider,
  setActiveProvider,
  addProvider,
  updateProvider,
  removeProvider,
  settings,
  updateSettings,
  completeCode,
  explainCode,
  generateCode,
  refactorCode
} = useKodeKilatAI();
```

#### 2. `AIAssistant` Component
Komponen UI utama untuk chat interface:
- Real-time messaging dengan AI
- Context display (file aktif, selected text)
- Message formatting dengan markdown support
- Copy/paste functionality
- Code insertion ke editor

#### 3. `AISettings` Component
Modal untuk konfigurasi AI providers dan settings:
- Provider management (add/edit/remove)
- API key configuration
- Model selection
- AI preferences (temperature, max tokens, etc.)

### Data Types

```typescript
interface AIProvider {
  id: string;
  name: string;
  type: 'openrouter' | 'huggingface' | 'ollama' | 'openai';
  apiKey?: string;
  baseUrl: string;
  models: string[];
  enabled: boolean;
}

interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  context?: CodeContext;
}

interface CodeContext {
  filePath: string;
  language: string;
  content: string;
  cursorPosition: number;
  selectedText?: string;
}

interface AISettings {
  temperature: number;
  maxTokens: number;
  autoComplete: boolean;
  enableChat: boolean;
  enableExplanations: boolean;
}
```

## 🚀 Usage

### Basic Chat
```typescript
import { AIAssistant } from '@/packages/ai';

<AIAssistant 
  currentFile="/path/to/file.ts"
  selectedText="const example = 'code'"
  onCodeInsert={(code) => insertIntoEditor(code)}
/>
```

### Using the Hook Directly
```typescript
import { useKodeKilatAI } from '@/packages/ai';

function MyComponent() {
  const { sendMessage, completeCode, explainCode } = useKodeKilatAI();
  
  const handleChat = async () => {
    await sendMessage("Explain this function", {
      filePath: "app.ts",
      language: "typescript",
      content: "function example() { return 'hello'; }",
      cursorPosition: 0
    });
  };
  
  const handleCompletion = async () => {
    const completion = await completeCode("function calculate", {
      filePath: "math.ts",
      language: "typescript",
      content: "// Math utilities",
      cursorPosition: 20
    });
  };
}
```

### Provider Management
```typescript
const { addProvider, updateProvider, setActiveProvider } = useKodeKilatAI();

// Add new provider
await addProvider({
  name: "My OpenRouter",
  type: "openrouter",
  apiKey: "sk-or-...",
  baseUrl: "https://openrouter.ai/api/v1",
  models: ["openai/gpt-4", "anthropic/claude-3-sonnet"],
  enabled: true
});

// Switch active provider
setActiveProvider("provider-id");

// Update provider settings
updateProvider("provider-id", {
  apiKey: "new-api-key",
  models: ["new-model-list"]
});
```

## 🔧 Configuration

### Default Providers

#### OpenRouter
- **Base URL**: `https://openrouter.ai/api/v1`
- **Models**: `openai/gpt-4`, `openai/gpt-3.5-turbo`, `anthropic/claude-3-sonnet`
- **API Key**: Required

#### HuggingFace
- **Base URL**: `https://api-inference.huggingface.co/models`
- **Models**: `codellama/CodeLlama-7b-Instruct-hf`, `microsoft/DialoGPT-medium`
- **API Key**: Optional (rate limited without key)

#### Ollama
- **Base URL**: `http://localhost:11434`
- **Models**: `llama2`, `codellama`, `mistral`
- **API Key**: Not required (local)

#### OpenAI
- **Base URL**: `https://api.openai.com/v1`
- **Models**: `gpt-4`, `gpt-3.5-turbo`
- **API Key**: Required

### Settings
```typescript
const defaultSettings: AISettings = {
  temperature: 0.7,
  maxTokens: 2000,
  autoComplete: true,
  enableChat: true,
  enableExplanations: true
};
```

## 🔌 API Integration

### Provider API Calls

#### OpenRouter/OpenAI Format
```typescript
const response = await fetch(`${provider.baseUrl}/chat/completions`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${provider.apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    model: selectedModel,
    messages: [{ role: 'user', content: message }],
    temperature: settings.temperature,
    max_tokens: settings.maxTokens
  })
});
```

#### HuggingFace Format
```typescript
const response = await fetch(`${provider.baseUrl}/${selectedModel}`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${provider.apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    inputs: message,
    parameters: {
      temperature: settings.temperature,
      max_new_tokens: settings.maxTokens
    }
  })
});
```

#### Ollama Format
```typescript
const response = await fetch(`${provider.baseUrl}/api/generate`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    model: selectedModel,
    prompt: message,
    stream: false,
    options: {
      temperature: settings.temperature,
      num_predict: settings.maxTokens
    }
  })
});
```

## 💾 Data Persistence

### localStorage Keys
- `kodekilat-ai-providers`: Array of AI providers
- `kodekilat-ai-active-provider`: ID of active provider
- `kodekilat-ai-settings`: AI settings object
- `kodekilat-ai-messages`: Chat message history

### Data Structure
```typescript
// Providers
localStorage.setItem('kodekilat-ai-providers', JSON.stringify(providers));

// Settings
localStorage.setItem('kodekilat-ai-settings', JSON.stringify(settings));

// Messages (with size limit)
const messages = JSON.parse(localStorage.getItem('kodekilat-ai-messages') || '[]');
if (messages.length > 100) {
  messages.splice(0, messages.length - 100); // Keep last 100 messages
}
localStorage.setItem('kodekilat-ai-messages', JSON.stringify(messages));
```

## 🎨 UI Components

### AIAssistant Features
- **Header**: Provider info, clear chat, settings button
- **Context Bar**: Current file and selected text info
- **Messages**: Chat history dengan formatting
- **Input**: Multi-line input dengan keyboard shortcuts
- **Actions**: Copy message, insert code, explain code

### AISettings Features
- **Provider List**: View all configured providers
- **Add Provider**: Form untuk menambah provider baru
- **Edit Provider**: Update existing provider settings
- **Active Provider**: Select which provider to use
- **Preferences**: AI behavior settings

## 🔄 Integration Points

### With File System
```typescript
// Get context from current file
const context: CodeContext = {
  filePath: currentFile,
  language: detectLanguage(currentFile),
  content: selectedText || fileContent,
  cursorPosition: editor.getCursorPosition(),
  selectedText: editor.getSelectedText()
};
```

### With Editor
```typescript
// Insert AI-generated code
const insertCode = (code: string) => {
  editor.insertText(code);
  editor.focus();
};

// Get selected text for context
const selectedText = editor.getSelectedText();
```

### With Sidebar
```typescript
// Toggle AI assistant in sidebar
<SidebarItem 
  icon={Zap} 
  label="AI Assistant"
  isActive={activeView === 'ai'}
  onClick={() => setActiveView('ai')}
/>
```

## 🚀 Next Steps

### Planned Enhancements
1. **Code Completion Integration**: Real-time completion dalam editor
2. **Inline Code Actions**: Quick actions pada selected code
3. **AI-Powered Debugging**: Debug assistance dengan AI
4. **Custom Prompts**: User-defined prompt templates
5. **Team Collaboration**: Shared AI conversations
6. **Performance Optimization**: Caching dan request optimization

### Integration Tasks
1. Integrate dengan Monaco Editor untuk real-time completion
2. Add keyboard shortcuts untuk AI actions
3. Implement AI-powered error detection
4. Add support untuk custom model fine-tuning
5. Create AI analytics dan usage tracking

## 📝 Status

✅ **COMPLETED**:
- Core AI hook implementation
- Multiple provider support
- Chat interface
- Settings management
- Data persistence
- TypeScript interfaces
- Error handling
- UI components

🔄 **IN PROGRESS**:
- Editor integration
- Real-time completion
- Keyboard shortcuts

📋 **TODO**:
- Performance optimization
- Advanced AI features
- Team collaboration
- Analytics tracking
