{"name": "kodekilat-studio", "version": "1.0.0", "description": "IDE Nusantara Modern, <PERSON><PERSON>, <PERSON>, dan <PERSON>-Time — 100% Offline", "private": true, "workspaces": ["apps/*", "packages/*", "shared/*"], "scripts": {"dev": "concurrently \"bun run dev:api\" \"bun run dev:ui\" \"bun run dev:electron\"", "dev:api": "cd apps/api-server && bun run dev", "dev:ui": "cd apps/studio/ui && bun run dev", "dev:electron": "cd apps/studio && bun run dev", "build": "bun run build:api && bun run build:ui && bun run build:electron", "build:api": "cd apps/api-server && bun run build", "build:ui": "cd apps/studio/ui && bun run build", "build:electron": "cd apps/studio && bun run build", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "dist:linux": "electron-builder --linux", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rimraf dist build .next node_modules/.cache", "clean:all": "bun run clean && rimraf node_modules", "lint:staged": "lint-staged", "format:staged": "lint-staged --config .lintstagedrc.json", "build:prod": "NODE_ENV=production bun run build", "postinstall": "husky install", "prepare": "husky install"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.8.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "typescript": "^5.3.0"}, "dependencies": {"better-sqlite3": "^9.2.2", "ws": "^8.14.2"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "repository": {"type": "git", "url": "https://github.com/kangpcode/kodekilat-studio.git"}, "keywords": ["ide", "editor", "nusantara", "indonesia", "electron", "nextjs", "bun", "ai", "collaboration", "vscode", "extensions"], "author": "KangPCode", "license": "MIT", "build": {"appId": "com.kodekilat.studio", "productName": "KodeKilat Studio", "directories": {"output": "dist"}, "files": ["apps/studio/dist/**/*", "apps/studio/ui/out/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "icon": "apps/studio/assets/icon.icns"}, "win": {"target": "nsis", "icon": "apps/studio/assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "apps/studio/assets/icon.png"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}}