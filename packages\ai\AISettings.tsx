import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Bot,
  User,
  Trash2,
  <PERSON>ting<PERSON>,
  Copy,
  Check,
  Loader2,
  Zap,
  Code,
  FileText,
  AlertCircle,
  Sparkles
} from 'lucide-react';
import { useKodeKilatAI, CodeContext } from './useKodeKilatAI';
import AISettings from './AISettings';

interface AIAssistantProps {
  currentFile?: string | null;
  selectedText?: string;
  className?: string;
  onCodeInsert?: (code: string) => void;
}

export function AIAssistant({
  currentFile,
  selectedText,
  className,
  onCodeInsert
}: AIAssistantProps) {
  const {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    providers,
    activeProvider,
    setActiveProvider,
    settings,
    updateSettings
  } = useKodeKilatAI();

  const [inputValue, setInputValue] = useState('');
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const detectLanguage = (filePath: string): string => {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'html': 'html',
      'css': 'css'
    };
    return languageMap[extension || ''] || 'plaintext';
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const context: CodeContext | undefined = currentFile ? {
      filePath: currentFile,
      language: detectLanguage(currentFile),
      content: selectedText || '',
      cursorPosition: 0,
      selectedText: selectedText || undefined
    } : undefined;

    try {
      await sendMessage(inputValue.trim(), context);
      setInputValue('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const copyMessage = async (content: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 2000);
    } catch (error) {
      console.error('Error copying message:', error);
    }
  };

  const insertCode = (code: string) => {
    onCodeInsert?.(code);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatMessage = (content: string) => {
    // Simple markdown-like formatting
    const lines = content.split('\n');
    const formatted = lines.map((line, index) => {
      // Code blocks
      if (line.startsWith('```
')) {
        return <div key={index} className="font-mono text-sm bg-muted p-2 rounded my-2">{line.slice(3)}</div>;
      }

      // Inline code
      if (line.includes('`')) {
        const parts = line.split('`');
        return (
          <div key={index}>
            {parts.map((part, i) =>
              i % 2 === 0 ? part : <code key={i} className="bg-muted px-1 rounded">{part}</code>
            )}
          </div>
        );
      }

      return <div key={index}>{line}</div>;
    });

    return formatted;
  };

  const getContextInfo = () => {
    const info = [];
    if (currentFile) {
      info.push(`File: ${currentFile.split('/').pop()}`);
    }
    if (selectedText) {
      info.push(`Selected: ${selectedText.length} chars`);
    }
    return info.join(' • ');
  };

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Zap className="w-5 h-5 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground">KodeKilat AI ⚡</h3>
            <p className="text-xs text-muted-foreground">
              {activeProvider?.name || 'No provider selected'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <button
            onClick={clearMessages}
            className="p-2 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
            title="Clear Chat"
          >
            <Trash2 className="w-4 h-4" />
          </button>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
            title="AI Settings"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="px-4 py-2 bg-red-50 border-b border-red-200">
          <div className="flex items-center space-x-2 text-sm text-red-600">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Context Info */}
      {(currentFile || selectedText) && (
        <div className="px-4 py-2 bg-muted/50 border-b border-border">
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <FileText className="w-3 h-3" />
            <span>{getContextInfo()}</span>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="ai-message"
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}>
                  {message.role === 'user' ? (
                    <User className="w-4 h-4" />
                  ) : (
                    <Bot className="w-4 h-4" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium">
                      {message.role === 'user' ? 'You' : 'KodeKilat AI'}
                    </span>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => copyMessage(message.content, message.id)}
                        className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors"
                        title="Copy message"
                      >
                        {copiedMessageId === message.id ? (
                          <Check className="w-3 h-3 text-green-500" />
                        ) : (
                          <Copy className="w-3 h-3" />
                        )}
                      </button>

                      {message.role === 'assistant' && message.content.includes('
```') && (
                        <button
                          onClick={() => {
                            const codeMatch = message.content.match(/```
[\s\S]*?
```/);
                            if (codeMatch) {
                              const code = codeMatch[0].replace(/```
\w*\n?/g, '').replace(/
```$/g, '');
                              insertCode(code);
                            }
                          }}
                          className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors"
                          title="Insert code"
                        >
                          <Code className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="text-sm text-foreground whitespace-pre-wrap">
                    {formatMessage(message.content)}
                  </div>

                  <div className="text-xs text-muted-foreground mt-1">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-3"
          >
            <div className="p-2 bg-muted rounded-lg">
              <Bot className="w-4 h-4" />
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>KodeKilat AI is thinking...</span>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ask KodeKilat AI anything about your code..."
              className="w-full p-3 bg-input border border-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
              rows={1}
              style={{ minHeight: '44px', maxHeight: '120px' }}
            />
          </div>

          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="p-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>

        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <span>{inputValue.length}/2000</span>
        </div>
      </div>
    </div>
  );
}

export default AIAssistant;
                  type="checkbox"
                          checked={settings.enableExplanations}
                          onChange={(e) => handleSettingsChange('enableExplanations', e.target.checked)}
                          className="rounded"
                        />
                        <span>Enable code explanations</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-border">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <AlertCircle className="w-4 h-4" />
              <span>Settings are saved automatically</span>
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Done
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

export default AISettings;
