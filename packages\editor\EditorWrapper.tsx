// import { motion } from 'framer-motion';
// import { FileText, Maximize2, Play, Plus, RotateCcw, Settings, Square, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

interface EditorWrapperProps {
  currentFile: string | null;
  onFileChange: (file: string | null) => void;
}

interface Tab {
  id: string;
  name: string;
  path: string;
  isDirty: boolean;
  language: string;
}

export function EditorWrapper({ currentFile, onFileChange }: EditorWrapperProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [tabs, setTabs] = useState<Tab[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [editorContent, setEditorContent] = useState('');

  // Remote collaboration integration (temporarily disabled for build)
  // const {
  //   isConnected,
  //   handleTextChange,
  //   handleCursorChange,
  //   fileCursors,
  //   activeFile: remoteActiveFile,
  // } = useRemoteEditorIntegration();
  const isConnected = false;
  const handleTextChange = () => {};
  const handleCursorChange = () => {};
  const fileCursors = {};
  const remoteActiveFile = null;

  // Mock file content
  const getFileContent = (filePath: string) => {
    const mockContent: Record<string, string> = {
      '/src/components/Header.tsx': `import React from 'react';
import { motion } from 'framer-motion';

interface HeaderProps {
  title: string;
}

export function Header({ title }: HeaderProps) {
  return (
    <header className="bg-background border-b border-border p-4">
      <h1 className="text-2xl font-bold text-foreground">{title}</h1>
    </header>
  );
}`,
      '/package.json': `{
  "name": "kodekilat-studio",
  "version": "1.0.0",
  "description": "IDE Nusantara Modern",
  "main": "index.js",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "framer-motion": "^10.0.0"
  }
}`,
      '/README.md': `# KodeKilat Studio ⚡

IDE Nusantara Modern, Ringan, Native, dan Real-Time — 100% Offline

## Features

- 🚀 Fast and lightweight
- 🤖 KodeKilat AI assistant
- 🎨 Visual UI designer
- 🔗 Real-time collaboration
- 📦 Extension support (.vsix & .kodix)

## Getting Started

\`\`\`bash
bun install
bun run dev
\`\`\`

## License

MIT License - Made with ❤️ in Indonesia`,
    };

    return mockContent[filePath] || `// File: ${filePath}\n// Content will be loaded here...`;
  };

  const getLanguageFromPath = (filePath: string) => {
    const ext = filePath.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      tsx: 'typescript',
      ts: 'typescript',
      jsx: 'javascript',
      js: 'javascript',
      json: 'json',
      md: 'markdown',
      css: 'css',
      html: 'html',
      py: 'python',
      rs: 'rust',
      go: 'go',
    };
    return languageMap[ext || ''] || 'plaintext';
  };

  const openFile = (filePath: string) => {
    const fileName = filePath.split('/').pop() || filePath;
    const language = getLanguageFromPath(filePath);

    // Check if tab already exists
    const existingTab = tabs.find(tab => tab.path === filePath);
    if (existingTab) {
      setActiveTab(existingTab.id);
      setEditorContent(getFileContent(filePath));
      onFileChange(filePath);
      return;
    }

    // Create new tab
    const newTab: Tab = {
      id: Date.now().toString(),
      name: fileName,
      path: filePath,
      isDirty: false,
      language,
    };

    setTabs(prev => [...prev, newTab]);
    setActiveTab(newTab.id);
    setEditorContent(getFileContent(filePath));
    onFileChange(filePath);
  };

  const closeTab = (tabId: string, e?: React.MouseEvent) => {
    e?.stopPropagation();

    const tabIndex = tabs.findIndex(tab => tab.id === tabId);
    const newTabs = tabs.filter(tab => tab.id !== tabId);
    setTabs(newTabs);

    if (activeTab === tabId) {
      if (newTabs.length > 0) {
        const newActiveIndex = Math.min(tabIndex, newTabs.length - 1);
        const newActiveTab = newTabs[newActiveIndex];
        setActiveTab(newActiveTab.id);
        setEditorContent(getFileContent(newActiveTab.path));
        onFileChange(newActiveTab.path);
      } else {
        setActiveTab(null);
        setEditorContent('');
        onFileChange(null);
      }
    }
  };

  const switchTab = (tabId: string) => {
    const tab = tabs.find(t => t.id === tabId);
    if (tab) {
      setActiveTab(tabId);
      setEditorContent(getFileContent(tab.path));
      onFileChange(tab.path);
    }
  };

  // Open file when currentFile changes from outside
  useEffect(() => {
    if (currentFile && !tabs.find(tab => tab.path === currentFile)) {
      openFile(currentFile);
    }
  }, [currentFile]);

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Tab Bar */}
      <div className="flex items-center bg-secondary border-b border-border min-h-[40px]">
        <div className="flex-1 flex items-center overflow-x-auto">
          {tabs.map(tab => (
            <div
              key={tab.id}
              className={`flex items-center space-x-2 px-3 py-2 border-r border-border cursor-pointer hover:bg-accent transition-colors min-w-0 ${
                activeTab === tab.id ? 'bg-background text-foreground' : 'text-muted-foreground'
              }`}
              onClick={() => switchTab(tab.id)}
            >
              <span className="text-xs">📄</span>
              <span className="text-sm truncate max-w-[120px]">{tab.name}</span>
              {tab.isDirty && <div className="w-2 h-2 bg-primary rounded-full" />}
              <button
                onClick={e => closeTab(tab.id, e)}
                className="p-0.5 hover:bg-accent rounded transition-colors"
              >
                <span className="text-xs">×</span>
              </button>
            </div>
          ))}
        </div>

        {/* Tab Actions */}
        <div className="flex items-center px-2">
          <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
            <span className="text-sm">+</span>
          </button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 flex">
        {activeTabData ? (
          <div className="flex-1 flex flex-col">
            {/* Editor Toolbar */}
            <div className="flex items-center justify-between px-4 py-2 bg-secondary/50 border-b border-border">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <span>{activeTabData.language}</span>
                <span>•</span>
                <span>{activeTabData.path}</span>

                {/* Collaboration Status */}
                {isConnected && (
                  <>
                    <span>•</span>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      <span className="text-green-500 font-medium">Live</span>
                    </div>
                  </>
                )}
              </div>
              <div className="flex items-center space-x-1">
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <span className="text-sm">▶</span>
                </button>
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <span className="text-sm">⏹</span>
                </button>
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <span className="text-sm">↻</span>
                </button>
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <span className="text-sm">⚙</span>
                </button>
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <span className="text-sm">⛶</span>
                </button>
              </div>
            </div>

            {/* Monaco Editor Placeholder */}
            <div className="flex-1 relative">
              <div ref={editorRef} className="absolute inset-0 bg-background">
                <textarea
                  value={editorContent}
                  onChange={e => {
                    const newValue = e.target.value;
                    setEditorContent(newValue);

                    // Send changes to remote collaboration if connected
                    if (isConnected && activeTabData) {
                      const changes = [
                        {
                          range: {
                            startLineNumber: 1,
                            startColumn: 1,
                            endLineNumber: 1,
                            endColumn: 1,
                          },
                          text: newValue,
                          rangeLength: editorContent.length,
                        },
                      ];
                      // handleTextChange(activeTabData.path, changes, Date.now());
                    }
                  }}
                  onSelect={e => {
                    // Handle cursor position changes for remote collaboration
                    if (isConnected && activeTabData) {
                      const target = e.target as HTMLTextAreaElement;
                      const position = {
                        lineNumber: 1,
                        column: target.selectionStart + 1,
                      };
                      const selection = {
                        startLineNumber: 1,
                        startColumn: target.selectionStart + 1,
                        endLineNumber: 1,
                        endColumn: target.selectionEnd + 1,
                      };
                      // handleCursorChange(activeTabData.path, position, selection);
                    }
                  }}
                  className="w-full h-full p-4 bg-transparent text-foreground font-mono text-sm resize-none focus:outline-none"
                  style={{ fontFamily: 'Fira Code, Monaco, Consolas, monospace' }}
                  placeholder="Start typing..."
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-5xl mb-4 opacity-50">📄</div>
              <h3 className="text-lg font-medium mb-2">No file open</h3>
              <p className="text-sm">Select a file from the explorer to start editing</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
