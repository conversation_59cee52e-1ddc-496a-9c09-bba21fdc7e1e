import { motion } from 'framer-motion';
import {
    <PERSON>Text,
    Maximize2,
    Play,
    Plus,
    RotateCcw,
    Settings,
    Square,
    X
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

interface EditorWrapperProps {
  currentFile: string | null;
  onFileChange: (file: string | null) => void;
}

interface Tab {
  id: string;
  name: string;
  path: string;
  isDirty: boolean;
  language: string;
}

export function EditorWrapper({ currentFile, onFileChange }: EditorWrapperProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [tabs, setTabs] = useState<Tab[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [editorContent, setEditorContent] = useState('');

  // Mock file content
  const getFileContent = (filePath: string) => {
    const mockContent: Record<string, string> = {
      '/src/components/Header.tsx': `import React from 'react';
import { motion } from 'framer-motion';

interface HeaderProps {
  title: string;
}

export function Header({ title }: HeaderProps) {
  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-background border-b border-border p-4"
    >
      <h1 className="text-2xl font-bold text-foreground">{title}</h1>
    </motion.header>
  );
}`,
      '/package.json': `{
  "name": "kodekilat-studio",
  "version": "1.0.0",
  "description": "IDE Nusantara Modern",
  "main": "index.js",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "framer-motion": "^10.0.0"
  }
}`,
      '/README.md': `# KodeKilat Studio ⚡

IDE Nusantara Modern, Ringan, Native, dan Real-Time — 100% Offline

## Features

- 🚀 Fast and lightweight
- 🤖 KodeKilat AI assistant
- 🎨 Visual UI designer
- 🔗 Real-time collaboration
- 📦 Extension support (.vsix & .kodix)

## Getting Started

\`\`\`bash
bun install
bun run dev
\`\`\`

## License

MIT License - Made with ❤️ in Indonesia`
    };

    return mockContent[filePath] || `// File: ${filePath}\n// Content will be loaded here...`;
  };

  const getLanguageFromPath = (filePath: string) => {
    const ext = filePath.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'tsx': 'typescript',
      'ts': 'typescript',
      'jsx': 'javascript',
      'js': 'javascript',
      'json': 'json',
      'md': 'markdown',
      'css': 'css',
      'html': 'html',
      'py': 'python',
      'rs': 'rust',
      'go': 'go'
    };
    return languageMap[ext || ''] || 'plaintext';
  };

  const openFile = (filePath: string) => {
    const fileName = filePath.split('/').pop() || filePath;
    const language = getLanguageFromPath(filePath);

    // Check if tab already exists
    const existingTab = tabs.find(tab => tab.path === filePath);
    if (existingTab) {
      setActiveTab(existingTab.id);
      setEditorContent(getFileContent(filePath));
      onFileChange(filePath);
      return;
    }

    // Create new tab
    const newTab: Tab = {
      id: Date.now().toString(),
      name: fileName,
      path: filePath,
      isDirty: false,
      language
    };

    setTabs(prev => [...prev, newTab]);
    setActiveTab(newTab.id);
    setEditorContent(getFileContent(filePath));
    onFileChange(filePath);
  };

  const closeTab = (tabId: string, e?: React.MouseEvent) => {
    e?.stopPropagation();

    const tabIndex = tabs.findIndex(tab => tab.id === tabId);
    const newTabs = tabs.filter(tab => tab.id !== tabId);
    setTabs(newTabs);

    if (activeTab === tabId) {
      if (newTabs.length > 0) {
        const newActiveIndex = Math.min(tabIndex, newTabs.length - 1);
        const newActiveTab = newTabs[newActiveIndex];
        setActiveTab(newActiveTab.id);
        setEditorContent(getFileContent(newActiveTab.path));
        onFileChange(newActiveTab.path);
      } else {
        setActiveTab(null);
        setEditorContent('');
        onFileChange(null);
      }
    }
  };

  const switchTab = (tabId: string) => {
    const tab = tabs.find(t => t.id === tabId);
    if (tab) {
      setActiveTab(tabId);
      setEditorContent(getFileContent(tab.path));
      onFileChange(tab.path);
    }
  };

  // Open file when currentFile changes from outside
  useEffect(() => {
    if (currentFile && !tabs.find(tab => tab.path === currentFile)) {
      openFile(currentFile);
    }
  }, [currentFile]);

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Tab Bar */}
      <div className="flex items-center bg-secondary border-b border-border min-h-[40px]">
        <div className="flex-1 flex items-center overflow-x-auto">
          {tabs.map((tab) => (
            <motion.div
              key={tab.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className={`flex items-center space-x-2 px-3 py-2 border-r border-border cursor-pointer hover:bg-accent transition-colors min-w-0 ${
                activeTab === tab.id ? 'bg-background text-foreground' : 'text-muted-foreground'
              }`}
              onClick={() => switchTab(tab.id)}
            >
              <FileText size={14} />
              <span className="text-sm truncate max-w-[120px]">{tab.name}</span>
              {tab.isDirty && <div className="w-2 h-2 bg-primary rounded-full" />}
              <button
                onClick={(e) => closeTab(tab.id, e)}
                className="p-0.5 hover:bg-accent rounded transition-colors"
              >
                <X size={12} />
              </button>
            </motion.div>
          ))}
        </div>

        {/* Tab Actions */}
        <div className="flex items-center px-2">
          <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
            <Plus size={16} />
          </button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 flex">
        {activeTabData ? (
          <div className="flex-1 flex flex-col">
            {/* Editor Toolbar */}
            <div className="flex items-center justify-between px-4 py-2 bg-secondary/50 border-b border-border">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <span>{activeTabData.language}</span>
                <span>•</span>
                <span>{activeTabData.path}</span>
              </div>
              <div className="flex items-center space-x-1">
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <Play size={16} />
                </button>
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <Square size={16} />
                </button>
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <RotateCcw size={16} />
                </button>
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <Settings size={16} />
                </button>
                <button className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors">
                  <Maximize2 size={16} />
                </button>
              </div>
            </div>

            {/* Monaco Editor Placeholder */}
            <div className="flex-1 relative">
              <div
                ref={editorRef}
                className="absolute inset-0 bg-background"
              >
                <textarea
                  value={editorContent}
                  onChange={(e) => setEditorContent(e.target.value)}
                  className="w-full h-full p-4 bg-transparent text-foreground font-mono text-sm resize-none focus:outline-none"
                  style={{ fontFamily: 'Fira Code, Monaco, Consolas, monospace' }}
                  placeholder="Start typing..."
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <FileText size={48} className="mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No file open</h3>
              <p className="text-sm">Select a file from the explorer to start editing</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
