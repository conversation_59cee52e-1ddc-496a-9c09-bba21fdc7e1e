import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Folder, 
  FolderOpen, 
  File, 
  Plus, 
  Search, 
  MoreHorizontal,
  ChevronRight,
  ChevronDown,
  FileText,
  Image,
  Code,
  Settings,
  Trash2,
  <PERSON><PERSON>,
  Scissors,
  Edit3
} from 'lucide-react';
import { FileItem, DirectoryTree } from '../../shared/types';

interface FileExplorerProps {
  workspacePath?: string;
  onFileSelect?: (filePath: string) => void;
  onFileCreate?: (filePath: string, type: 'file' | 'directory') => void;
  onFileDelete?: (filePath: string) => void;
  onFileRename?: (oldPath: string, newPath: string) => void;
  className?: string;
}

interface TreeNode extends DirectoryTree {
  expanded?: boolean;
  level: number;
}

export function FileExplorer({ 
  workspacePath, 
  onFileSelect, 
  onFileCreate, 
  onFileDelete, 
  onFileRename,
  className 
}: FileExplorerProps) {
  const [fileTree, setFileTree] = useState<TreeNode[]>([]);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    filePath: string;
    type: 'file' | 'directory';
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [clipboard, setClipboard] = useState<{
    path: string;
    operation: 'copy' | 'cut';
  } | null>(null);

  useEffect(() => {
    if (workspacePath) {
      loadFileTree();
    }
  }, [workspacePath]);

  const loadFileTree = async () => {
    if (!workspacePath) return;
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/fs/tree?path=${encodeURIComponent(workspacePath)}`);
      if (response.ok) {
        const data = await response.json();
        const tree = buildTreeNodes(data.tree || [], 0);
        setFileTree(tree);
      }
    } catch (error) {
      console.error('Error loading file tree:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const buildTreeNodes = (items: (FileItem | DirectoryTree)[], level: number): TreeNode[] => {
    return items.map(item => ({
      ...item,
      level,
      expanded: level === 0, // Expand root level by default
      children: item.type === 'directory' && 'children' in item 
        ? buildTreeNodes(item.children, level + 1)
        : []
    }));
  };

  const toggleDirectory = (path: string) => {
    setFileTree(prev => updateTreeNode(prev, path, node => ({
      ...node,
      expanded: !node.expanded
    })));
  };

  const updateTreeNode = (
    nodes: TreeNode[], 
    targetPath: string, 
    updater: (node: TreeNode) => TreeNode
  ): TreeNode[] => {
    return nodes.map(node => {
      if (node.path === targetPath) {
        return updater(node);
      }
      if (node.children.length > 0) {
        return {
          ...node,
          children: updateTreeNode(node.children, targetPath, updater)
        };
      }
      return node;
    });
  };

  const handleFileClick = (filePath: string, type: 'file' | 'directory') => {
    if (type === 'directory') {
      toggleDirectory(filePath);
    } else {
      setSelectedFile(filePath);
      onFileSelect?.(filePath);
    }
  };

  const handleContextMenu = (e: React.MouseEvent, filePath: string, type: 'file' | 'directory') => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      filePath,
      type
    });
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  const createNewFile = async (parentPath: string, type: 'file' | 'directory') => {
    const name = prompt(`Enter ${type} name:`);
    if (!name) return;

    const newPath = `${parentPath}/${name}`;
    
    try {
      const response = await fetch('/api/fs/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ path: newPath, type })
      });

      if (response.ok) {
        await loadFileTree();
        onFileCreate?.(newPath, type);
      }
    } catch (error) {
      console.error('Error creating file:', error);
    }
    
    closeContextMenu();
  };

  const deleteFile = async (filePath: string) => {
    if (!confirm(`Are you sure you want to delete ${filePath}?`)) return;

    try {
      const response = await fetch('/api/fs/delete', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ path: filePath })
      });

      if (response.ok) {
        await loadFileTree();
        onFileDelete?.(filePath);
        if (selectedFile === filePath) {
          setSelectedFile(null);
        }
      }
    } catch (error) {
      console.error('Error deleting file:', error);
    }
    
    closeContextMenu();
  };

  const renameFile = async (filePath: string) => {
    const currentName = filePath.split('/').pop() || '';
    const newName = prompt('Enter new name:', currentName);
    if (!newName || newName === currentName) return;

    const newPath = filePath.replace(/[^/]+$/, newName);
    
    try {
      const response = await fetch('/api/fs/rename', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ oldPath: filePath, newPath })
      });

      if (response.ok) {
        await loadFileTree();
        onFileRename?.(filePath, newPath);
        if (selectedFile === filePath) {
          setSelectedFile(newPath);
        }
      }
    } catch (error) {
      console.error('Error renaming file:', error);
    }
    
    closeContextMenu();
  };

  const copyFile = (filePath: string) => {
    setClipboard({ path: filePath, operation: 'copy' });
    closeContextMenu();
  };

  const cutFile = (filePath: string) => {
    setClipboard({ path: filePath, operation: 'cut' });
    closeContextMenu();
  };

  const pasteFile = async (targetPath: string) => {
    if (!clipboard) return;

    const fileName = clipboard.path.split('/').pop() || '';
    const newPath = `${targetPath}/${fileName}`;

    try {
      const response = await fetch('/api/fs/paste', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sourcePath: clipboard.path,
          targetPath: newPath,
          operation: clipboard.operation
        })
      });

      if (response.ok) {
        await loadFileTree();
        if (clipboard.operation === 'cut') {
          setClipboard(null);
        }
      }
    } catch (error) {
      console.error('Error pasting file:', error);
    }
    
    closeContextMenu();
  };

  const getFileIcon = (fileName: string, type: 'file' | 'directory') => {
    if (type === 'directory') {
      return <Folder className="w-4 h-4 text-blue-400" />;
    }

    const extension = fileName.split('.').pop()?.toLowerCase();
    const iconMap: Record<string, React.ReactNode> = {
      'js': <Code className="w-4 h-4 text-yellow-400" />,
      'jsx': <Code className="w-4 h-4 text-blue-400" />,
      'ts': <Code className="w-4 h-4 text-blue-600" />,
      'tsx': <Code className="w-4 h-4 text-blue-600" />,
      'py': <Code className="w-4 h-4 text-green-400" />,
      'java': <Code className="w-4 h-4 text-red-400" />,
      'cpp': <Code className="w-4 h-4 text-blue-500" />,
      'c': <Code className="w-4 h-4 text-blue-500" />,
      'html': <Code className="w-4 h-4 text-orange-400" />,
      'css': <Code className="w-4 h-4 text-blue-400" />,
      'json': <FileText className="w-4 h-4 text-yellow-400" />,
      'md': <FileText className="w-4 h-4 text-blue-400" />,
      'txt': <FileText className="w-4 h-4 text-gray-400" />,
      'png': <Image className="w-4 h-4 text-purple-400" />,
      'jpg': <Image className="w-4 h-4 text-purple-400" />,
      'jpeg': <Image className="w-4 h-4 text-purple-400" />,
      'gif': <Image className="w-4 h-4 text-purple-400" />,
      'svg': <Image className="w-4 h-4 text-green-400" />
    };

    return iconMap[extension || ''] || <File className="w-4 h-4 text-gray-400" />;
  };

  const renderTreeNode = (node: TreeNode) => {
    const isSelected = selectedFile === node.path;
    const hasChildren = node.type === 'directory' && node.children.length > 0;

    return (
      <div key={node.path}>
        <motion.div
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          className={`file-tree-item ${isSelected ? 'selected' : ''}`}
          style={{ paddingLeft: `${node.level * 16 + 8}px` }}
          onClick={() => handleFileClick(node.path, node.type)}
          onContextMenu={(e) => handleContextMenu(e, node.path, node.type)}
        >
          <div className="flex items-center space-x-1 flex-1 min-w-0">
            {node.type === 'directory' && (
              <button className="p-0.5 hover:bg-accent rounded">
                {node.expanded ? (
                  <ChevronDown className="w-3 h-3" />
                ) : (
                  <ChevronRight className="w-3 h-3" />
                )}
              </button>
            )}
            
            {getFileIcon(node.name, node.type)}
            
            <span className="truncate text-sm">{node.name}</span>
          </div>
        </motion.div>

        <AnimatePresence>
          {node.type === 'directory' && node.expanded && hasChildren && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              {node.children.map(child => renderTreeNode(child))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  const filteredTree = searchQuery
    ? fileTree.filter(node => 
        node.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : fileTree;

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Header */}
      <div className="p-3 border-b border-border">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-sm">Explorer</h3>
          <button
            onClick={() => workspacePath && createNewFile(workspacePath, 'file')}
            className="p-1 hover:bg-accent hover:text-accent-foreground rounded transition-colors"
            title="New File"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
        
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-8 pr-3 py-1.5 text-sm bg-input border border-border rounded focus:outline-none focus:ring-1 focus:ring-primary"
          />
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-sm text-muted-foreground">Loading files...</div>
          </div>
        ) : filteredTree.length > 0 ? (
          <div className="py-2">
            {filteredTree.map(node => renderTreeNode(node))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <Folder className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
              <div className="text-sm text-muted-foreground">
                {searchQuery ? 'No files found' : 'No workspace opened'}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Context Menu */}
      <AnimatePresence>
        {contextMenu && (
          <>
            <div
              className="fixed inset-0 z-40"
              onClick={closeContextMenu}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="context-menu fixed z-50"
              style={{ left: contextMenu.x, top: contextMenu.y }}
            >
              <button
                onClick={() => createNewFile(contextMenu.filePath, 'file')}
                className="context-menu-item"
              >
                <FileText className="w-4 h-4 mr-2" />
                New File
              </button>
              
              <button
                onClick={() => createNewFile(contextMenu.filePath, 'directory')}
                className="context-menu-item"
              >
                <Folder className="w-4 h-4 mr-2" />
                New Folder
              </button>
              
              <div className="context-menu-separator" />
              
              <button
                onClick={() => copyFile(contextMenu.filePath)}
                className="context-menu-item"
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy
              </button>
              
              <button
                onClick={() => cutFile(contextMenu.filePath)}
                className="context-menu-item"
              >
                <Scissors className="w-4 h-4 mr-2" />
                Cut
              </button>
              
              {clipboard && (
                <button
                  onClick={() => pasteFile(contextMenu.filePath)}
                  className="context-menu-item"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Paste
                </button>
              )}
              
              <div className="context-menu-separator" />
              
              <button
                onClick={() => renameFile(contextMenu.filePath)}
                className="context-menu-item"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Rename
              </button>
              
              <button
                onClick={() => deleteFile(contextMenu.filePath)}
                className="context-menu-item text-destructive"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </button>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}

export default FileExplorer;
