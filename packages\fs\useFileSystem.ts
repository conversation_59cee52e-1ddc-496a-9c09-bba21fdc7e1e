import { useCallback, useEffect, useState } from 'react';

export interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  modified: string;
  created: string;
  children?: FileSystemItem[];
  expanded?: boolean;
}

export interface FileSystemHookResult {
  files: FileSystemItem[];
  loading: boolean;
  error: string | null;
  currentPath: string;
  listDirectory: (path?: string) => Promise<void>;
  readFile: (path: string) => Promise<string>;
  writeFile: (path: string, content: string) => Promise<void>;
  createDirectory: (path: string) => Promise<void>;
  deleteItem: (path: string) => Promise<void>;
  renameItem: (oldPath: string, newPath: string) => Promise<void>;
  refreshDirectory: () => Promise<void>;
  openFolder: () => Promise<string | null>;
}

const API_BASE = 'http://localhost:3001';

export function useFileSystem(initialPath?: string): FileSystemHookResult {
  const [files, setFiles] = useState<FileSystemItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPath, setCurrentPath] = useState(initialPath || '');

  const apiCall = async (endpoint: string, options?: RequestInit) => {
    try {
      const response = await fetch(`${API_BASE}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Unknown error';
      setError(message);
      throw err;
    }
  };

  const listDirectory = useCallback(async (path?: string) => {
    const targetPath = path || currentPath;
    if (!targetPath) return;

    setLoading(true);
    setError(null);

    try {
      const data = await apiCall(`/api/fs/list?path=${encodeURIComponent(targetPath)}`);
      setFiles(data.files || []);
      setCurrentPath(targetPath);
    } catch (err) {
      console.error('Error listing directory:', err);
    } finally {
      setLoading(false);
    }
  }, [currentPath]);

  const readFile = useCallback(async (path: string): Promise<string> => {
    setError(null);
    try {
      const data = await apiCall(`/api/fs/read?path=${encodeURIComponent(path)}`);
      return data.content || '';
    } catch (err) {
      console.error('Error reading file:', err);
      throw err;
    }
  }, []);

  const writeFile = useCallback(async (path: string, content: string) => {
    setError(null);
    try {
      await apiCall('/api/fs/write', {
        method: 'POST',
        body: JSON.stringify({ path, content }),
      });
      // Refresh directory if the file is in current directory
      if (path.startsWith(currentPath)) {
        await listDirectory();
      }
    } catch (err) {
      console.error('Error writing file:', err);
      throw err;
    }
  }, [currentPath, listDirectory]);

  const createDirectory = useCallback(async (path: string) => {
    setError(null);
    try {
      await apiCall('/api/fs/mkdir', {
        method: 'POST',
        body: JSON.stringify({ path }),
      });
      await listDirectory();
    } catch (err) {
      console.error('Error creating directory:', err);
      throw err;
    }
  }, [listDirectory]);

  const deleteItem = useCallback(async (path: string) => {
    setError(null);
    try {
      await apiCall(`/api/fs/delete?path=${encodeURIComponent(path)}`, {
        method: 'DELETE',
      });
      await listDirectory();
    } catch (err) {
      console.error('Error deleting item:', err);
      throw err;
    }
  }, [listDirectory]);

  const renameItem = useCallback(async (oldPath: string, newPath: string) => {
    setError(null);
    try {
      await apiCall('/api/fs/rename', {
        method: 'POST',
        body: JSON.stringify({ oldPath, newPath }),
      });
      await listDirectory();
    } catch (err) {
      console.error('Error renaming item:', err);
      throw err;
    }
  }, [listDirectory]);

  const refreshDirectory = useCallback(async () => {
    await listDirectory(currentPath);
  }, [currentPath, listDirectory]);

  const openFolder = useCallback(async (): Promise<string | null> => {
    try {
      // Use Electron's dialog API to open folder
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.openFolder();
        if (result && !result.canceled && result.filePaths.length > 0) {
          const folderPath = result.filePaths[0];
          await listDirectory(folderPath);
          return folderPath;
        }
      }
      return null;
    } catch (err) {
      console.error('Error opening folder:', err);
      setError('Failed to open folder');
      return null;
    }
  }, [listDirectory]);

  // Auto-load directory on mount if path is provided
  useEffect(() => {
    if (initialPath) {
      listDirectory(initialPath);
    }
  }, [initialPath, listDirectory]);

  return {
    files,
    loading,
    error,
    currentPath,
    listDirectory,
    readFile,
    writeFile,
    createDirectory,
    deleteItem,
    renameItem,
    refreshDirectory,
    openFolder,
  };
}
