# @kodekilat/remote

Remote collaboration and live share package for KodeKilat Studio ⚡

## Features

- **Real-time Collaboration**: Live coding sessions with multiple participants
- **Voice Communication**: WebRTC-based voice calls with mute/unmute controls
- **Screen Sharing**: Share your screen with other participants
- **Live Chat**: Built-in text chat with support for code snippets and files
- **Cursor Synchronization**: See other participants' cursors and selections in real-time
- **File Synchronization**: Real-time file content synchronization with conflict resolution
- **Permission Management**: Role-based access control (owner, collaborator, viewer)
- **Session Management**: Create, join, and manage collaboration sessions

## Installation

```bash
pnpm add @kodekilat/remote
```

## Quick Start

### 1. Setup Provider

Wrap your application with the `RemoteCollaborationProvider`:

```tsx
import { RemoteCollaborationProvider } from '@kodekilat/remote';

function App() {
  return (
    <RemoteCollaborationProvider enabled={true}>
      <YourApp />
    </RemoteCollaborationProvider>
  );
}
```

### 2. Session Management

Use the `RemoteSessionManager` component for session creation and management:

```tsx
import { RemoteSessionManager } from '@kodekilat/remote';

function Sidebar() {
  return (
    <div className="sidebar">
      <RemoteSessionManager />
    </div>
  );
}
```

### 3. Editor Integration

Integrate with Monaco Editor for real-time collaboration:

```tsx
import { useRemoteEditorIntegration } from '@kodekilat/remote';
import { Editor } from '@monaco-editor/react';

function CodeEditor() {
  const {
    isConnected,
    handleTextChange,
    handleCursorChange,
    fileCursors
  } = useRemoteEditorIntegration();

  return (
    <Editor
      onChange={(value, event) => {
        if (event.changes) {
          handleTextChange('current-file.js', event.changes, event.versionId);
        }
      }}
      onCursorPositionChanged={(e) => {
        handleCursorChange('current-file.js', e.position, e.selection);
      }}
      // Show other participants' cursors
      decorations={fileCursors['current-file.js'] || []}
    />
  );
}
```

## Components

### RemoteSessionManager
Main component for session management with create/join dialogs.

### RemoteChat
Floating chat component with support for text messages, code snippets, and file sharing.

### VoiceControls
Voice call controls with mute/unmute, deafen, and call management.

### ScreenShare
Screen sharing component with fullscreen support and viewer controls.

## Hooks

### useRemoteSession()
Main hook for accessing all remote collaboration state and actions.

### useRemoteConnection()
Hook for connection status and session management.

### useRemoteParticipants()
Hook for accessing participants list and online status.

### useRemoteChat()
Hook for chat functionality and message management.

### useRemoteVoice()
Hook for voice call controls and audio management.

### useRemoteScreen()
Hook for screen sharing functionality.

### useRemoteEditorIntegration()
Hook for integrating with code editors (Monaco, CodeMirror, etc.).

### useRemoteFileExplorer()
Hook for file explorer integration showing active files and editors.

### useRemoteSidebar()
Hook for sidebar integration with participant count and session info.

## API Integration

The package integrates with the backend API routes:

- `POST /api/remote/sessions` - Create new session
- `POST /api/remote/sessions/:id/join` - Join session
- `DELETE /api/remote/sessions/:id/leave` - Leave session
- `POST /api/remote/sessions/:id/text-changes` - Send text changes
- `POST /api/remote/sessions/:id/cursor` - Send cursor position
- `GET /api/remote/sessions/public` - Get public sessions

## WebSocket Events

Real-time communication via Socket.io:

- `user-joined` - User joined session
- `user-left` - User left session
- `text-changes` - Text changes broadcast
- `cursor-position` - Cursor position updates
- `chat-message` - Chat messages
- `voice-call-start` - Voice call initiated
- `screen-share-start` - Screen sharing started

## Configuration

### Environment Variables

```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
```

### TypeScript Configuration

The package includes comprehensive TypeScript definitions for all collaboration features.

## Examples

### Creating a Session

```tsx
import { useRemoteSession } from '@kodekilat/remote';

function CreateSessionButton() {
  const { createSession, connect } = useRemoteSession();

  const handleCreate = async () => {
    const sessionId = await createSession(
      '/path/to/project',
      'My Coding Session',
      false // private session
    );
    
    await connect(sessionId, 'Session Owner');
  };

  return <button onClick={handleCreate}>Create Session</button>;
}
```

### Joining a Session

```tsx
import { useRemoteSession } from '@kodekilat/remote';

function JoinSessionButton() {
  const { joinSession } = useRemoteSession();

  const handleJoin = async () => {
    await joinSession('session-abc123', 'Participant Name', 'password');
  };

  return <button onClick={handleJoin}>Join Session</button>;
}
```

### Sending Chat Messages

```tsx
import { useRemoteChat } from '@kodekilat/remote';

function ChatInput() {
  const { sendChatMessage } = useRemoteChat();
  const [message, setMessage] = useState('');

  const handleSend = () => {
    sendChatMessage(message);
    setMessage('');
  };

  return (
    <div>
      <input 
        value={message} 
        onChange={(e) => setMessage(e.target.value)} 
      />
      <button onClick={handleSend}>Send</button>
    </div>
  );
}
```

## Styling

The package uses Tailwind CSS classes and follows the shadcn/ui design system. Make sure your project has Tailwind CSS configured.

Required Tailwind CSS classes:
- Color utilities (bg-*, text-*, border-*)
- Layout utilities (flex, grid, absolute, etc.)
- Animation utilities (animate-pulse, animate-spin)
- Responsive utilities (sm:, md:, lg:)

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

WebRTC features require modern browser support for:
- MediaDevices API
- RTCPeerConnection
- getUserMedia
- getDisplayMedia (for screen sharing)

## License

MIT License - see LICENSE file for details.
