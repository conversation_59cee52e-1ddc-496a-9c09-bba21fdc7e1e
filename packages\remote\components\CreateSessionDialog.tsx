import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Folder, Globe, Lock, Users, Settings } from 'lucide-react';
import { useRemoteSession } from '../hooks/useRemoteSession';
import { cn } from '../utils/cn';

interface CreateSessionDialogProps {
  onClose: () => void;
}

export function CreateSessionDialog({ onClose }: CreateSessionDialogProps) {
  const { createSession, connect } = useRemoteSession();
  const [formData, setFormData] = useState({
    sessionName: '',
    projectPath: '',
    isPublic: false,
    password: '',
    maxParticipants: 10,
    allowVoiceChat: true,
    allowScreenShare: true,
    permissions: {
      defaultRead: true,
      defaultWrite: true,
      requireApproval: false
    }
  });
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.sessionName.trim()) {
      setError('Session name is required');
      return;
    }
    
    if (!formData.projectPath.trim()) {
      setError('Project path is required');
      return;
    }

    setIsCreating(true);
    setError('');

    try {
      const sessionId = await createSession(
        formData.projectPath,
        formData.sessionName,
        formData.isPublic
      );

      // Auto-join the created session
      await connect(sessionId, 'Session Owner');
      
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create session');
    } finally {
      setIsCreating(false);
    }
  };

  const selectProjectFolder = () => {
    // In a real implementation, this would open a folder picker
    // For now, we'll use a placeholder
    setFormData(prev => ({
      ...prev,
      projectPath: '/path/to/project'
    }));
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="bg-background border border-border rounded-lg shadow-lg w-full max-w-md mx-4"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-semibold">Create Collaboration Session</h2>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onClose}
            className="p-1 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="w-5 h-5" />
          </motion.button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Session Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Session Name</label>
            <input
              type="text"
              value={formData.sessionName}
              onChange={(e) => setFormData(prev => ({ ...prev, sessionName: e.target.value }))}
              placeholder="My Coding Session"
              className="w-full px-3 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary"
              required
            />
          </div>

          {/* Project Path */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Project Folder</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={formData.projectPath}
                onChange={(e) => setFormData(prev => ({ ...prev, projectPath: e.target.value }))}
                placeholder="/path/to/your/project"
                className="flex-1 px-3 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
              <motion.button
                type="button"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={selectProjectFolder}
                className="px-3 py-2 border border-border rounded-md hover:bg-accent transition-colors"
              >
                <Folder className="w-4 h-4" />
              </motion.button>
            </div>
          </div>

          {/* Session Type */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Session Type</label>
            <div className="flex gap-2">
              <motion.button
                type="button"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setFormData(prev => ({ ...prev, isPublic: false }))}
                className={cn(
                  "flex-1 p-3 border rounded-md transition-colors",
                  !formData.isPublic 
                    ? "border-primary bg-primary/10 text-primary" 
                    : "border-border hover:bg-accent"
                )}
              >
                <div className="flex items-center gap-2">
                  <Lock className="w-4 h-4" />
                  <span className="text-sm font-medium">Private</span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Invite-only session
                </p>
              </motion.button>

              <motion.button
                type="button"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setFormData(prev => ({ ...prev, isPublic: true }))}
                className={cn(
                  "flex-1 p-3 border rounded-md transition-colors",
                  formData.isPublic 
                    ? "border-primary bg-primary/10 text-primary" 
                    : "border-border hover:bg-accent"
                )}
              >
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  <span className="text-sm font-medium">Public</span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Anyone can join
                </p>
              </motion.button>
            </div>
          </div>

          {/* Password (for private sessions) */}
          {!formData.isPublic && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Password (Optional)</label>
              <input
                type="password"
                value={formData.password}
                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                placeholder="Leave empty for no password"
                className="w-full px-3 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
          )}

          {/* Advanced Settings */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Settings className="w-4 h-4 text-muted-foreground" />
              <label className="text-sm font-medium">Advanced Settings</label>
            </div>
            
            <div className="space-y-3 pl-6">
              {/* Max Participants */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Max Participants</span>
                <input
                  type="number"
                  min="2"
                  max="50"
                  value={formData.maxParticipants}
                  onChange={(e) => setFormData(prev => ({ ...prev, maxParticipants: parseInt(e.target.value) }))}
                  className="w-16 px-2 py-1 border border-border rounded text-sm bg-background"
                />
              </div>

              {/* Voice Chat */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Allow Voice Chat</span>
                <input
                  type="checkbox"
                  checked={formData.allowVoiceChat}
                  onChange={(e) => setFormData(prev => ({ ...prev, allowVoiceChat: e.target.checked }))}
                  className="rounded"
                />
              </div>

              {/* Screen Share */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Allow Screen Share</span>
                <input
                  type="checkbox"
                  checked={formData.allowScreenShare}
                  onChange={(e) => setFormData(prev => ({ ...prev, allowScreenShare: e.target.checked }))}
                  className="rounded"
                />
              </div>

              {/* Default Permissions */}
              <div className="space-y-2">
                <span className="text-sm">Default Permissions</span>
                <div className="space-y-1 pl-4">
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Read Access</span>
                    <input
                      type="checkbox"
                      checked={formData.permissions.defaultRead}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        permissions: { ...prev.permissions, defaultRead: e.target.checked }
                      }))}
                      className="rounded"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Write Access</span>
                    <input
                      type="checkbox"
                      checked={formData.permissions.defaultWrite}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        permissions: { ...prev.permissions, defaultWrite: e.target.checked }
                      }))}
                      className="rounded"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Require Approval</span>
                    <input
                      type="checkbox"
                      checked={formData.permissions.requireApproval}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        permissions: { ...prev.permissions, requireApproval: e.target.checked }
                      }))}
                      className="rounded"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm text-destructive">{error}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            <motion.button
              type="button"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-border rounded-md hover:bg-accent transition-colors"
            >
              Cancel
            </motion.button>
            
            <motion.button
              type="submit"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={isCreating}
              className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50"
            >
              {isCreating ? 'Creating...' : 'Create Session'}
            </motion.button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  );
}
