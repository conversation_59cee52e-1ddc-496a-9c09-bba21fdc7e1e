import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Users, Globe, Lock, Search, Loader2 } from 'lucide-react';
import { useRemoteSession } from '../hooks/useRemoteSession';
import { cn } from '../utils/cn';

interface JoinSessionDialogProps {
  onClose: () => void;
}

export function JoinSessionDialog({ onClose }: JoinSessionDialogProps) {
  const { joinSession } = useRemoteSession();
  const [activeTab, setActiveTab] = useState<'code' | 'browse'>('code');
  const [formData, setFormData] = useState({
    sessionCode: '',
    userName: '',
    password: ''
  });
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState('');
  const [publicSessions, setPublicSessions] = useState<any[]>([]);
  const [loadingSessions, setLoadingSessions] = useState(false);

  const handleJoinByCode = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.sessionCode.trim()) {
      setError('Session code is required');
      return;
    }
    
    if (!formData.userName.trim()) {
      setError('Your name is required');
      return;
    }

    setIsJoining(true);
    setError('');

    try {
      // Convert share code to session ID
      const sessionId = `session-${formData.sessionCode.toLowerCase()}`;
      
      await joinSession(sessionId, formData.userName, formData.password || undefined);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join session');
    } finally {
      setIsJoining(false);
    }
  };

  const handleJoinSession = async (sessionId: string) => {
    if (!formData.userName.trim()) {
      setError('Please enter your name first');
      return;
    }

    setIsJoining(true);
    setError('');

    try {
      await joinSession(sessionId, formData.userName);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join session');
    } finally {
      setIsJoining(false);
    }
  };

  const loadPublicSessions = async () => {
    setLoadingSessions(true);
    try {
      const response = await fetch('/api/remote/sessions/public');
      const sessions = await response.json();
      setPublicSessions(sessions);
    } catch (err) {
      console.error('Failed to load public sessions:', err);
    } finally {
      setLoadingSessions(false);
    }
  };

  React.useEffect(() => {
    if (activeTab === 'browse') {
      loadPublicSessions();
    }
  }, [activeTab]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="bg-background border border-border rounded-lg shadow-lg w-full max-w-md mx-4"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-semibold">Join Collaboration Session</h2>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onClose}
            className="p-1 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="w-5 h-5" />
          </motion.button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setActiveTab('code')}
            className={cn(
              "flex-1 px-4 py-3 text-sm font-medium transition-colors",
              activeTab === 'code'
                ? "border-b-2 border-primary text-primary"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            Join by Code
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setActiveTab('browse')}
            className={cn(
              "flex-1 px-4 py-3 text-sm font-medium transition-colors",
              activeTab === 'browse'
                ? "border-b-2 border-primary text-primary"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            Browse Public
          </motion.button>
        </div>

        {/* Content */}
        <div className="p-6">
          {activeTab === 'code' ? (
            <form onSubmit={handleJoinByCode} className="space-y-4">
              {/* Your Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Your Name</label>
                <input
                  type="text"
                  value={formData.userName}
                  onChange={(e) => setFormData(prev => ({ ...prev, userName: e.target.value }))}
                  placeholder="Enter your name"
                  className="w-full px-3 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>

              {/* Session Code */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Session Code</label>
                <input
                  type="text"
                  value={formData.sessionCode}
                  onChange={(e) => setFormData(prev => ({ ...prev, sessionCode: e.target.value.toUpperCase() }))}
                  placeholder="ABC123"
                  className="w-full px-3 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary font-mono text-center text-lg tracking-wider"
                  maxLength={6}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Enter the 6-character code shared by the session owner
                </p>
              </div>

              {/* Password */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Password (if required)</label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Enter password"
                  className="w-full px-3 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              {/* Error Message */}
              {error && (
                <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                  <p className="text-sm text-destructive">{error}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-2 pt-4">
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-border rounded-md hover:bg-accent transition-colors"
                >
                  Cancel
                </motion.button>
                
                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  disabled={isJoining}
                  className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
                >
                  {isJoining && <Loader2 className="w-4 h-4 animate-spin" />}
                  {isJoining ? 'Joining...' : 'Join Session'}
                </motion.button>
              </div>
            </form>
          ) : (
            <div className="space-y-4">
              {/* Your Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Your Name</label>
                <input
                  type="text"
                  value={formData.userName}
                  onChange={(e) => setFormData(prev => ({ ...prev, userName: e.target.value }))}
                  placeholder="Enter your name"
                  className="w-full px-3 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>

              {/* Public Sessions List */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Public Sessions</label>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={loadPublicSessions}
                    disabled={loadingSessions}
                    className="p-1 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {loadingSessions ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Search className="w-4 h-4" />
                    )}
                  </motion.button>
                </div>

                <div className="border border-border rounded-md max-h-64 overflow-y-auto">
                  {loadingSessions ? (
                    <div className="p-4 text-center text-muted-foreground">
                      <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
                      Loading sessions...
                    </div>
                  ) : publicSessions.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      <Globe className="w-6 h-6 mx-auto mb-2" />
                      No public sessions available
                    </div>
                  ) : (
                    <div className="divide-y divide-border">
                      {publicSessions.map((session) => (
                        <motion.div
                          key={session.id}
                          whileHover={{ backgroundColor: 'rgba(255,255,255,0.05)' }}
                          className="p-3 cursor-pointer"
                          onClick={() => handleJoinSession(session.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <h4 className="text-sm font-medium truncate">{session.name}</h4>
                              <div className="flex items-center gap-2 mt-1">
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <Users className="w-3 h-3" />
                                  {session.participants?.length || 0} participants
                                </div>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  {session.isPublic ? (
                                    <>
                                      <Globe className="w-3 h-3" />
                                      Public
                                    </>
                                  ) : (
                                    <>
                                      <Lock className="w-3 h-3" />
                                      Private
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleJoinSession(session.id);
                              }}
                              disabled={isJoining}
                              className="px-3 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors disabled:opacity-50"
                            >
                              Join
                            </motion.button>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                  <p className="text-sm text-destructive">{error}</p>
                </div>
              )}

              {/* Cancel Button */}
              <div className="pt-4">
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onClose}
                  className="w-full px-4 py-2 border border-border rounded-md hover:bg-accent transition-colors"
                >
                  Cancel
                </motion.button>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
}
