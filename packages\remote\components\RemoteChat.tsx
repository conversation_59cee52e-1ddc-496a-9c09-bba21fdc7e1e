import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Smile, 
  Paperclip, 
  Code, 
  FileText,
  Image,
  X,
  MessageCircle,
  Users
} from 'lucide-react';
import { useRemoteChat, useRemoteParticipants } from '../hooks/useRemoteSession';
import { cn } from '../utils/cn';
import type { ChatMessage } from '../types';

interface RemoteChatProps {
  className?: string;
}

export function RemoteChat({ className }: RemoteChatProps) {
  const { chatMessages, showChat, sendChatMessage, toggleChat } = useRemoteChat();
  const { participants } = useRemoteParticipants();
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  useEffect(() => {
    if (showChat && inputRef.current) {
      inputRef.current.focus();
    }
  }, [showChat]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) return;

    sendChatMessage(message.trim());
    setMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  const getParticipantName = (userId: string) => {
    const participant = participants.find(p => p.id === userId);
    return participant?.name || 'Unknown User';
  };

  const getParticipantColor = (userId: string) => {
    const participant = participants.find(p => p.id === userId);
    return participant?.color || '#6B7280';
  };

  if (!showChat) {
    return (
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={toggleChat}
        className={cn(
          "fixed bottom-4 right-4 p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:bg-primary/90 transition-colors z-40",
          className
        )}
        title="Open Chat"
      >
        <MessageCircle className="w-5 h-5" />
        {chatMessages.length > 0 && (
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-destructive text-destructive-foreground rounded-full flex items-center justify-center text-xs">
            {chatMessages.length > 9 ? '9+' : chatMessages.length}
          </div>
        )}
      </motion.button>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 300 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 300 }}
      className={cn(
        "fixed right-4 bottom-4 w-80 h-96 bg-background border border-border rounded-lg shadow-lg flex flex-col z-40",
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b">
        <div className="flex items-center gap-2">
          <MessageCircle className="w-4 h-4 text-primary" />
          <h3 className="text-sm font-medium">Chat</h3>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Users className="w-3 h-3" />
            {participants.length}
          </div>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={toggleChat}
          className="p-1 text-muted-foreground hover:text-foreground transition-colors"
        >
          <X className="w-4 h-4" />
        </motion.button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-3 space-y-3">
        <AnimatePresence>
          {chatMessages.map((msg) => (
            <ChatMessageItem
              key={msg.id}
              message={msg}
              participantName={getParticipantName(msg.userId)}
              participantColor={getParticipantColor(msg.userId)}
            />
          ))}
        </AnimatePresence>
        
        {chatMessages.length === 0 && (
          <div className="text-center text-muted-foreground py-8">
            <MessageCircle className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No messages yet</p>
            <p className="text-xs">Start the conversation!</p>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <form onSubmit={handleSendMessage} className="p-3 border-t">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-3 py-2 pr-20 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary text-sm"
              maxLength={500}
            />
            
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
              <motion.button
                type="button"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-1 text-muted-foreground hover:text-foreground transition-colors"
                title="Add Emoji"
              >
                <Smile className="w-3 h-3" />
              </motion.button>
              
              <motion.button
                type="button"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-1 text-muted-foreground hover:text-foreground transition-colors"
                title="Attach File"
              >
                <Paperclip className="w-3 h-3" />
              </motion.button>
            </div>
          </div>
          
          <motion.button
            type="submit"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            disabled={!message.trim()}
            className="p-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50"
          >
            <Send className="w-4 h-4" />
          </motion.button>
        </div>
      </form>
    </motion.div>
  );
}

interface ChatMessageItemProps {
  message: ChatMessage;
  participantName: string;
  participantColor: string;
}

function ChatMessageItem({ message, participantName, participantColor }: ChatMessageItemProps) {
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getMessageIcon = () => {
    switch (message.type) {
      case 'code':
        return <Code className="w-3 h-3" />;
      case 'file':
        return <FileText className="w-3 h-3" />;
      case 'system':
        return null;
      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={cn(
        "group",
        message.type === 'system' && "text-center"
      )}
    >
      {message.type === 'system' ? (
        <div className="text-xs text-muted-foreground bg-muted/50 rounded-full px-3 py-1 inline-block">
          {message.message}
        </div>
      ) : (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium text-white"
              style={{ backgroundColor: participantColor }}
            >
              {participantName.charAt(0).toUpperCase()}
            </div>
            
            <span className="text-xs font-medium">{participantName}</span>
            
            <span className="text-xs text-muted-foreground">
              {formatTime(message.timestamp)}
            </span>
            
            {getMessageIcon() && (
              <div className="text-muted-foreground">
                {getMessageIcon()}
              </div>
            )}
          </div>
          
          <div className={cn(
            "ml-8 text-sm",
            message.type === 'code' && "bg-muted p-2 rounded font-mono text-xs"
          )}>
            {message.type === 'code' ? (
              <pre className="whitespace-pre-wrap">{message.message}</pre>
            ) : (
              <p className="whitespace-pre-wrap">{message.message}</p>
            )}
          </div>
        </div>
      )}
    </motion.div>
  );
}

// Quick Actions Component
export function ChatQuickActions() {
  const { sendChatMessage } = useRemoteChat();

  const quickActions = [
    {
      label: 'Share Code',
      icon: Code,
      action: () => {
        // In a real implementation, this would get the selected code
        sendChatMessage('```javascript\nconsole.log("Hello, World!");\n```', 'code');
      }
    },
    {
      label: 'Share File',
      icon: FileText,
      action: () => {
        // In a real implementation, this would open a file picker
        sendChatMessage('Shared: example.js', 'file');
      }
    },
    {
      label: 'Share Screenshot',
      icon: Image,
      action: () => {
        // In a real implementation, this would capture a screenshot
        sendChatMessage('Shared a screenshot', 'file');
      }
    }
  ];

  return (
    <div className="flex gap-1">
      {quickActions.map((action) => (
        <motion.button
          key={action.label}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={action.action}
          className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors"
          title={action.label}
        >
          <action.icon className="w-4 h-4" />
        </motion.button>
      ))}
    </div>
  );
}
