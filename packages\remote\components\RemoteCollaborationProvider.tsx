import React, { createContext, useContext, useEffect, useState } from 'react';
import { AnimatePresence } from 'framer-motion';
import { 
  useRemoteSession, 
  useRemoteConnection, 
  useRemoteChat, 
  useRemoteVoice, 
  useRemoteScreen 
} from '../hooks/useRemoteSession';
import { RemoteChat } from './RemoteChat';
import { VoiceControls, VoiceCallInvitation } from './VoiceControls';
import { ScreenShare, ScreenShareRequest } from './ScreenShare';
import type { CollaborationSession } from '../types';

interface RemoteCollaborationContextType {
  isCollaborationEnabled: boolean;
  currentSession: CollaborationSession | null;
  enableCollaboration: () => void;
  disableCollaboration: () => void;
}

const RemoteCollaborationContext = createContext<RemoteCollaborationContextType | null>(null);

export function useRemoteCollaboration() {
  const context = useContext(RemoteCollaborationContext);
  if (!context) {
    throw new Error('useRemoteCollaboration must be used within RemoteCollaborationProvider');
  }
  return context;
}

interface RemoteCollaborationProviderProps {
  children: React.ReactNode;
  enabled?: boolean;
}

export function RemoteCollaborationProvider({ 
  children, 
  enabled = true 
}: RemoteCollaborationProviderProps) {
  const [isCollaborationEnabled, setIsCollaborationEnabled] = useState(enabled);
  const { currentSession, isConnected } = useRemoteConnection();
  const { showChat } = useRemoteChat();
  const { showVoiceControls, voiceCall } = useRemoteVoice();
  const { screenShare } = useRemoteScreen();
  
  // State for incoming requests
  const [incomingVoiceCall, setIncomingVoiceCall] = useState<{
    fromUser: string;
    callId: string;
  } | null>(null);
  
  const [incomingScreenShare, setIncomingScreenShare] = useState<{
    fromUser: string;
    requestId: string;
  } | null>(null);

  const enableCollaboration = () => {
    setIsCollaborationEnabled(true);
  };

  const disableCollaboration = () => {
    setIsCollaborationEnabled(false);
  };

  // Handle incoming voice call
  const handleAcceptVoiceCall = () => {
    if (incomingVoiceCall) {
      // Accept voice call logic would go here
      console.log('Accepting voice call from:', incomingVoiceCall.fromUser);
      setIncomingVoiceCall(null);
    }
  };

  const handleDeclineVoiceCall = () => {
    if (incomingVoiceCall) {
      // Decline voice call logic would go here
      console.log('Declining voice call from:', incomingVoiceCall.fromUser);
      setIncomingVoiceCall(null);
    }
  };

  // Handle incoming screen share request
  const handleAcceptScreenShare = () => {
    if (incomingScreenShare) {
      // Accept screen share logic would go here
      console.log('Accepting screen share from:', incomingScreenShare.fromUser);
      setIncomingScreenShare(null);
    }
  };

  const handleDeclineScreenShare = () => {
    if (incomingScreenShare) {
      // Decline screen share logic would go here
      console.log('Declining screen share from:', incomingScreenShare.fromUser);
      setIncomingScreenShare(null);
    }
  };

  const contextValue: RemoteCollaborationContextType = {
    isCollaborationEnabled,
    currentSession,
    enableCollaboration,
    disableCollaboration
  };

  if (!isCollaborationEnabled) {
    return (
      <RemoteCollaborationContext.Provider value={contextValue}>
        {children}
      </RemoteCollaborationContext.Provider>
    );
  }

  return (
    <RemoteCollaborationContext.Provider value={contextValue}>
      {children}
      
      {/* Remote Collaboration UI Components */}
      <AnimatePresence>
        {isConnected && (
          <>
            {/* Chat Component */}
            <RemoteChat />
            
            {/* Voice Controls */}
            {showVoiceControls && <VoiceControls />}
            
            {/* Screen Share */}
            {screenShare.isActive && <ScreenShare />}
            
            {/* Incoming Voice Call */}
            {incomingVoiceCall && (
              <VoiceCallInvitation
                fromUser={incomingVoiceCall.fromUser}
                onAccept={handleAcceptVoiceCall}
                onDecline={handleDeclineVoiceCall}
              />
            )}
            
            {/* Incoming Screen Share Request */}
            {incomingScreenShare && (
              <ScreenShareRequest
                fromUser={incomingScreenShare.fromUser}
                onAccept={handleAcceptScreenShare}
                onDecline={handleDeclineScreenShare}
              />
            )}
          </>
        )}
      </AnimatePresence>
      
      {/* Collaboration Status Indicator */}
      {isConnected && <CollaborationStatusIndicator />}
    </RemoteCollaborationContext.Provider>
  );
}

function CollaborationStatusIndicator() {
  const { currentSession, isConnected } = useRemoteConnection();
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Auto-hide after 5 seconds
    const timer = setTimeout(() => {
      setIsVisible(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, [isConnected]);

  if (!isConnected || !currentSession || !isVisible) {
    return null;
  }

  return (
    <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-full shadow-lg z-40 flex items-center gap-2">
      <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
      <span className="text-sm font-medium">
        Connected to "{currentSession.name}"
      </span>
      <button
        onClick={() => setIsVisible(false)}
        className="ml-2 text-white/80 hover:text-white"
      >
        ×
      </button>
    </div>
  );
}

// Hook for editor integration
export function useRemoteEditor() {
  const { 
    activeFile, 
    fileCursors, 
    fileVersions, 
    sendTextChanges, 
    sendCursorPosition, 
    requestFileSync 
  } = useRemoteSession();
  
  const { isConnected } = useRemoteConnection();

  // Function to handle text changes in Monaco Editor
  const handleTextChange = (filePath: string, changes: any[], version: number) => {
    if (!isConnected) return;
    
    sendTextChanges(filePath, changes, version);
  };

  // Function to handle cursor position changes
  const handleCursorChange = (filePath: string, position: any, selection: any) => {
    if (!isConnected) return;
    
    sendCursorPosition(filePath, {
      line: position.lineNumber,
      column: position.column,
      selection: selection ? {
        startLine: selection.startLineNumber,
        startColumn: selection.startColumn,
        endLine: selection.endLineNumber,
        endColumn: selection.endColumn
      } : null
    });
  };

  // Function to sync file content
  const syncFile = (filePath: string) => {
    if (!isConnected) return;
    
    requestFileSync(filePath);
  };

  return {
    isConnected,
    activeFile,
    fileCursors,
    fileVersions,
    handleTextChange,
    handleCursorChange,
    syncFile
  };
}

// Hook for file explorer integration
export function useRemoteFileExplorer() {
  const { currentSession } = useRemoteConnection();
  
  // Get files that are currently being edited by others
  const getActiveFiles = () => {
    if (!currentSession) return [];
    
    return Object.entries(currentSession.files || {}).map(([path, fileState]) => ({
      path,
      isActive: fileState.isOpen,
      editedBy: fileState.editedBy,
      lastModified: fileState.lastModified,
      hasUnsavedChanges: fileState.hasUnsavedChanges
    }));
  };

  // Get participants currently editing a specific file
  const getFileEditors = (filePath: string) => {
    if (!currentSession?.files[filePath]) return [];
    
    return currentSession.files[filePath].editedBy || [];
  };

  return {
    activeFiles: getActiveFiles(),
    getFileEditors
  };
}

// Hook for sidebar integration
export function useRemoteSidebar() {
  const { participants, currentSession } = useRemoteSession();
  const { isConnected } = useRemoteConnection();
  
  return {
    isConnected,
    currentSession,
    participants,
    participantCount: participants.length,
    onlineCount: participants.filter(p => p.isOnline).length
  };
}
