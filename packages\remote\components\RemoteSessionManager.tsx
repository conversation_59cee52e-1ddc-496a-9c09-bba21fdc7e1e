import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Share2, 
  MessageCircle, 
  Mic, 
  MicOff, 
  Video, 
  VideoOff,
  Monitor,
  MonitorOff,
  Settings,
  UserPlus,
  LogOut,
  Copy,
  Check
} from 'lucide-react';
import { useRemoteSession, useRemoteConnection, useRemoteParticipants } from '../hooks/useRemoteSession';
import { cn } from '../utils/cn';

interface RemoteSessionManagerProps {
  className?: string;
}

export function RemoteSessionManager({ className }: RemoteSessionManagerProps) {
  const { isConnected, connectionStatus, disconnect } = useRemoteConnection();
  const { participants, currentSession } = useRemoteParticipants();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showJoinDialog, setShowJoinDialog] = useState(false);

  if (!isConnected || !currentSession) {
    return (
      <div className={cn("flex flex-col h-full bg-background", className)}>
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold text-foreground">Remote Collaboration</h2>
          <p className="text-sm text-muted-foreground">Start or join a live coding session</p>
        </div>
        
        <div className="flex-1 flex flex-col items-center justify-center p-6 space-y-4">
          <div className="text-center space-y-2">
            <Share2 className="w-12 h-12 text-muted-foreground mx-auto" />
            <h3 className="text-lg font-medium">No Active Session</h3>
            <p className="text-sm text-muted-foreground max-w-sm">
              Create a new collaboration session or join an existing one to start coding together.
            </p>
          </div>
          
          <div className="flex gap-2">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setShowCreateDialog(true)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Create Session
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setShowJoinDialog(true)}
              className="px-4 py-2 border border-border rounded-md hover:bg-accent transition-colors"
            >
              Join Session
            </motion.button>
          </div>
        </div>

        <AnimatePresence>
          {showCreateDialog && (
            <CreateSessionDialog 
              onClose={() => setShowCreateDialog(false)}
            />
          )}
          
          {showJoinDialog && (
            <JoinSessionDialog 
              onClose={() => setShowJoinDialog(false)}
            />
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Session Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-foreground">{currentSession.name}</h2>
            <p className="text-sm text-muted-foreground">
              {participants.length} participant{participants.length !== 1 ? 's' : ''}
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <SessionControls />
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={disconnect}
              className="p-2 text-muted-foreground hover:text-destructive transition-colors"
              title="Leave Session"
            >
              <LogOut className="w-4 h-4" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Participants List */}
      <div className="flex-1 overflow-y-auto">
        <ParticipantsList participants={participants} />
      </div>

      {/* Session Info */}
      <div className="p-4 border-t">
        <SessionInfo session={currentSession} />
      </div>
    </div>
  );
}

function SessionControls() {
  const { 
    voiceCall, 
    screenShare, 
    startVoiceCall, 
    endVoiceCall, 
    toggleMute,
    startScreenShare,
    stopScreenShare 
  } = useRemoteSession();

  return (
    <div className="flex items-center gap-1">
      {/* Voice Call Controls */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={voiceCall.isActive ? endVoiceCall : startVoiceCall}
        className={cn(
          "p-2 rounded-md transition-colors",
          voiceCall.isActive 
            ? "bg-green-500 text-white hover:bg-green-600" 
            : "text-muted-foreground hover:text-foreground hover:bg-accent"
        )}
        title={voiceCall.isActive ? "End Voice Call" : "Start Voice Call"}
      >
        {voiceCall.isActive ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
      </motion.button>

      {/* Screen Share Controls */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={screenShare.isSharing ? stopScreenShare : startScreenShare}
        className={cn(
          "p-2 rounded-md transition-colors",
          screenShare.isSharing 
            ? "bg-blue-500 text-white hover:bg-blue-600" 
            : "text-muted-foreground hover:text-foreground hover:bg-accent"
        )}
        title={screenShare.isSharing ? "Stop Screen Share" : "Start Screen Share"}
      >
        {screenShare.isSharing ? <Monitor className="w-4 h-4" /> : <MonitorOff className="w-4 h-4" />}
      </motion.button>

      {/* Chat Toggle */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => {}}
        className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors"
        title="Toggle Chat"
      >
        <MessageCircle className="w-4 h-4" />
      </motion.button>

      {/* Settings */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => {}}
        className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors"
        title="Session Settings"
      >
        <Settings className="w-4 h-4" />
      </motion.button>
    </div>
  );
}

interface ParticipantsListProps {
  participants: any[];
}

function ParticipantsList({ participants }: ParticipantsListProps) {
  return (
    <div className="p-4 space-y-2">
      <h3 className="text-sm font-medium text-muted-foreground mb-3">Participants</h3>
      
      {participants.map((participant) => (
        <motion.div
          key={participant.id}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          className="flex items-center gap-3 p-2 rounded-md hover:bg-accent transition-colors"
        >
          <div className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
            participant.isOnline ? "bg-green-500 text-white" : "bg-muted text-muted-foreground"
          )}>
            {participant.name.charAt(0).toUpperCase()}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium truncate">{participant.name}</span>
              {participant.role === 'owner' && (
                <span className="text-xs bg-primary text-primary-foreground px-1.5 py-0.5 rounded">
                  Owner
                </span>
              )}
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <div className={cn(
                "w-2 h-2 rounded-full",
                participant.isOnline ? "bg-green-500" : "bg-muted-foreground"
              )} />
              {participant.isOnline ? 'Online' : 'Offline'}
            </div>
          </div>
          
          {participant.role === 'owner' && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-1 text-muted-foreground hover:text-foreground transition-colors"
              title="Manage Permissions"
            >
              <Settings className="w-3 h-3" />
            </motion.button>
          )}
        </motion.div>
      ))}
    </div>
  );
}

interface SessionInfoProps {
  session: any;
}

function SessionInfo({ session }: SessionInfoProps) {
  const [copied, setCopied] = useState(false);
  
  const shareCode = session.id.slice(-6).toUpperCase();
  const joinUrl = `kodekilat://join-session/${session.id}`;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium text-muted-foreground">Session Info</h3>
      
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Share Code:</span>
          <div className="flex items-center gap-2">
            <code className="text-sm bg-muted px-2 py-1 rounded">{shareCode}</code>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => copyToClipboard(shareCode)}
              className="p-1 text-muted-foreground hover:text-foreground transition-colors"
            >
              {copied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
            </motion.button>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Join URL:</span>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => copyToClipboard(joinUrl)}
            className="p-1 text-muted-foreground hover:text-foreground transition-colors"
            title="Copy Join URL"
          >
            {copied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
          </motion.button>
        </div>
      </div>
    </div>
  );
}

// Placeholder components - will be implemented in separate files
function CreateSessionDialog({ onClose }: { onClose: () => void }) {
  return <div>Create Session Dialog</div>;
}

function JoinSessionDialog({ onClose }: { onClose: () => void }) {
  return <div>Join Session Dialog</div>;
}
