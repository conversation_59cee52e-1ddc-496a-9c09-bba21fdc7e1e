import React, { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Monitor, 
  MonitorOff, 
  Maximize2, 
  Minimize2, 
  Volume2, 
  VolumeX,
  Settings,
  X,
  Users,
  Loader2
} from 'lucide-react';
import { useRemoteScreen, useRemoteParticipants } from '../hooks/useRemoteSession';
import { cn } from '../utils/cn';

interface ScreenShareProps {
  className?: string;
}

export function ScreenShare({ className }: ScreenShareProps) {
  const { screenShare, startScreenShare, stopScreenShare } = useRemoteScreen();
  const { participants } = useRemoteParticipants();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (screenShare.stream && videoRef.current) {
      videoRef.current.srcObject = screenShare.stream;
    }
  }, [screenShare.stream]);

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  const handleStartShare = async () => {
    try {
      await startScreenShare();
    } catch (error) {
      console.error('Failed to start screen share:', error);
    }
  };

  if (!screenShare.isActive && !screenShare.isSharing) {
    return (
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={handleStartShare}
        className={cn(
          "p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:bg-primary/90 transition-colors",
          className
        )}
        title="Start Screen Share"
      >
        <Monitor className="w-5 h-5" />
      </motion.button>
    );
  }

  return (
    <AnimatePresence>
      {screenShare.isActive && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className={cn(
            "fixed inset-4 bg-background border border-border rounded-lg shadow-2xl z-50 flex flex-col",
            isFullscreen && "inset-0 rounded-none",
            className
          )}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur">
            <div className="flex items-center gap-3">
              <Monitor className="w-5 h-5 text-primary" />
              <div>
                <h3 className="text-lg font-semibold">
                  {screenShare.isSharing ? 'Sharing Your Screen' : `${screenShare.sharedBy}'s Screen`}
                </h3>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="w-3 h-3" />
                  {participants.length} viewer{participants.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <ScreenShareControls
                isSharing={screenShare.isSharing}
                isMuted={isMuted}
                isFullscreen={isFullscreen}
                onToggleMute={() => setIsMuted(!isMuted)}
                onToggleFullscreen={toggleFullscreen}
                onStopShare={stopScreenShare}
              />
            </div>
          </div>

          {/* Video Container */}
          <div className="flex-1 relative bg-black">
            {screenShare.isLoading ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                  <p className="text-sm">
                    {screenShare.isSharing ? 'Starting screen share...' : 'Connecting to screen share...'}
                  </p>
                </div>
              </div>
            ) : screenShare.stream ? (
              <video
                ref={videoRef}
                autoPlay
                muted={isMuted}
                className="w-full h-full object-contain"
                onLoadedMetadata={() => {
                  if (videoRef.current) {
                    videoRef.current.play();
                  }
                }}
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <MonitorOff className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No Screen Share Active</p>
                  <p className="text-sm opacity-75">
                    {screenShare.isSharing 
                      ? 'Failed to capture screen' 
                      : 'Waiting for screen share to start'
                    }
                  </p>
                </div>
              </div>
            )}

            {/* Overlay Controls */}
            {!isFullscreen && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2">
                <ScreenShareOverlay
                  isSharing={screenShare.isSharing}
                  onStopShare={stopScreenShare}
                />
              </div>
            )}
          </div>

          {/* Participants Bar */}
          {!isFullscreen && (
            <div className="p-3 border-t bg-background/95 backdrop-blur">
              <ScreenShareParticipants participants={participants} />
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface ScreenShareControlsProps {
  isSharing: boolean;
  isMuted: boolean;
  isFullscreen: boolean;
  onToggleMute: () => void;
  onToggleFullscreen: () => void;
  onStopShare: () => void;
}

function ScreenShareControls({
  isSharing,
  isMuted,
  isFullscreen,
  onToggleMute,
  onToggleFullscreen,
  onStopShare
}: ScreenShareControlsProps) {
  return (
    <div className="flex items-center gap-2">
      {/* Audio Toggle */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onToggleMute}
        className={cn(
          "p-2 rounded-md transition-colors",
          isMuted 
            ? "bg-destructive text-destructive-foreground" 
            : "bg-muted text-muted-foreground hover:bg-accent"
        )}
        title={isMuted ? "Unmute" : "Mute"}
      >
        {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
      </motion.button>

      {/* Fullscreen Toggle */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onToggleFullscreen}
        className="p-2 bg-muted text-muted-foreground hover:bg-accent rounded-md transition-colors"
        title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
      >
        {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
      </motion.button>

      {/* Settings */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => {}}
        className="p-2 bg-muted text-muted-foreground hover:bg-accent rounded-md transition-colors"
        title="Screen Share Settings"
      >
        <Settings className="w-4 h-4" />
      </motion.button>

      {/* Stop Share (only for sharer) */}
      {isSharing && (
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onStopShare}
          className="p-2 bg-destructive text-destructive-foreground hover:bg-destructive/90 rounded-md transition-colors"
          title="Stop Sharing"
        >
          <MonitorOff className="w-4 h-4" />
        </motion.button>
      )}

      {/* Close (for viewers) */}
      {!isSharing && (
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onStopShare}
          className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors"
          title="Close Screen Share"
        >
          <X className="w-4 h-4" />
        </motion.button>
      )}
    </div>
  );
}

interface ScreenShareOverlayProps {
  isSharing: boolean;
  onStopShare: () => void;
}

function ScreenShareOverlay({ isSharing, onStopShare }: ScreenShareOverlayProps) {
  if (!isSharing) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-destructive text-destructive-foreground px-4 py-2 rounded-full shadow-lg flex items-center gap-2"
    >
      <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
      <span className="text-sm font-medium">You are sharing your screen</span>
      
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onStopShare}
        className="ml-2 px-3 py-1 bg-white/20 hover:bg-white/30 rounded-full text-xs transition-colors"
      >
        Stop
      </motion.button>
    </motion.div>
  );
}

interface ScreenShareParticipantsProps {
  participants: any[];
}

function ScreenShareParticipants({ participants }: ScreenShareParticipantsProps) {
  return (
    <div className="flex items-center gap-3">
      <span className="text-sm font-medium text-muted-foreground">Viewers:</span>
      
      <div className="flex items-center gap-2">
        {participants.slice(0, 5).map((participant) => (
          <div
            key={participant.id}
            className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium"
            title={participant.name}
          >
            {participant.name.charAt(0).toUpperCase()}
          </div>
        ))}
        
        {participants.length > 5 && (
          <div className="w-6 h-6 rounded-full bg-muted text-muted-foreground flex items-center justify-center text-xs">
            +{participants.length - 5}
          </div>
        )}
      </div>
      
      <span className="text-xs text-muted-foreground">
        {participants.length} total
      </span>
    </div>
  );
}

// Screen Share Request Component
export function ScreenShareRequest({ 
  fromUser, 
  onAccept, 
  onDecline 
}: {
  fromUser: string;
  onAccept: () => void;
  onDecline: () => void;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed top-4 right-4 bg-background border border-border rounded-lg shadow-lg p-4 z-50"
    >
      <div className="flex items-center gap-3 mb-3">
        <Monitor className="w-5 h-5 text-primary" />
        <div>
          <h4 className="text-sm font-medium">Screen Share Request</h4>
          <p className="text-xs text-muted-foreground">{fromUser} wants to share their screen</p>
        </div>
      </div>
      
      <div className="flex gap-2">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onDecline}
          className="flex-1 px-3 py-2 border border-border rounded-md hover:bg-accent transition-colors"
        >
          Decline
        </motion.button>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onAccept}
          className="flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          View
        </motion.button>
      </div>
    </motion.div>
  );
}
