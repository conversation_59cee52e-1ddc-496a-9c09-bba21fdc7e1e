import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Mi<PERSON>, 
  MicOff, 
  Volume2, 
  VolumeX, 
  Phone, 
  PhoneOff,
  Settings,
  Users,
  Minimize2,
  Maximize2
} from 'lucide-react';
import { useRemoteVoice, useRemoteParticipants } from '../hooks/useRemoteSession';
import { cn } from '../utils/cn';

interface VoiceControlsProps {
  className?: string;
}

export function VoiceControls({ className }: VoiceControlsProps) {
  const { 
    voiceCall, 
    showVoiceControls, 
    startVoiceCall, 
    endVoiceCall, 
    toggleMute,
    toggleVoiceControls 
  } = useRemoteVoice();
  const { participants } = useRemoteParticipants();
  const [isMinimized, setIsMinimized] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);

  // Simulate audio level for demo
  useEffect(() => {
    if (voiceCall.isActive && !voiceCall.isMuted) {
      const interval = setInterval(() => {
        setAudioLevel(Math.random() * 100);
      }, 100);
      return () => clearInterval(interval);
    } else {
      setAudioLevel(0);
    }
  }, [voiceCall.isActive, voiceCall.isMuted]);

  if (!showVoiceControls) {
    return null;
  }

  const activeParticipants = participants.filter(p => 
    voiceCall.participants.includes(p.id)
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 50 }}
      className={cn(
        "fixed bottom-20 left-1/2 -translate-x-1/2 bg-background border border-border rounded-lg shadow-lg z-40",
        isMinimized ? "w-auto" : "w-80",
        className
      )}
    >
      {isMinimized ? (
        <MinimizedVoiceControls 
          voiceCall={voiceCall}
          onExpand={() => setIsMinimized(false)}
          onEndCall={endVoiceCall}
          onToggleMute={toggleMute}
          audioLevel={audioLevel}
        />
      ) : (
        <ExpandedVoiceControls
          voiceCall={voiceCall}
          participants={activeParticipants}
          onMinimize={() => setIsMinimized(true)}
          onClose={toggleVoiceControls}
          onEndCall={endVoiceCall}
          onToggleMute={toggleMute}
          audioLevel={audioLevel}
        />
      )}
    </motion.div>
  );
}

interface MinimizedVoiceControlsProps {
  voiceCall: any;
  onExpand: () => void;
  onEndCall: () => void;
  onToggleMute: () => void;
  audioLevel: number;
}

function MinimizedVoiceControls({ 
  voiceCall, 
  onExpand, 
  onEndCall, 
  onToggleMute,
  audioLevel 
}: MinimizedVoiceControlsProps) {
  return (
    <div className="flex items-center gap-2 p-3">
      {/* Audio Level Indicator */}
      <div className="flex items-center gap-1">
        <div className="w-1 h-4 bg-muted rounded-full overflow-hidden">
          <motion.div
            className="w-full bg-green-500 rounded-full"
            style={{ height: `${audioLevel}%` }}
            animate={{ height: `${audioLevel}%` }}
            transition={{ duration: 0.1 }}
          />
        </div>
      </div>

      {/* Mute Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onToggleMute}
        className={cn(
          "p-2 rounded-full transition-colors",
          voiceCall.isMuted 
            ? "bg-destructive text-destructive-foreground" 
            : "bg-green-500 text-white"
        )}
      >
        {voiceCall.isMuted ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
      </motion.button>

      {/* End Call Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onEndCall}
        className="p-2 bg-destructive text-destructive-foreground rounded-full hover:bg-destructive/90 transition-colors"
      >
        <PhoneOff className="w-4 h-4" />
      </motion.button>

      {/* Expand Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onExpand}
        className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-full transition-colors"
      >
        <Maximize2 className="w-4 h-4" />
      </motion.button>
    </div>
  );
}

interface ExpandedVoiceControlsProps {
  voiceCall: any;
  participants: any[];
  onMinimize: () => void;
  onClose: () => void;
  onEndCall: () => void;
  onToggleMute: () => void;
  audioLevel: number;
}

function ExpandedVoiceControls({ 
  voiceCall, 
  participants, 
  onMinimize, 
  onClose, 
  onEndCall, 
  onToggleMute,
  audioLevel 
}: ExpandedVoiceControlsProps) {
  return (
    <div className="p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Phone className="w-4 h-4 text-green-500" />
          <h3 className="text-sm font-medium">Voice Call</h3>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Users className="w-3 h-3" />
            {participants.length}
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onMinimize}
            className="p-1 text-muted-foreground hover:text-foreground transition-colors"
            title="Minimize"
          >
            <Minimize2 className="w-4 h-4" />
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onClose}
            className="p-1 text-muted-foreground hover:text-foreground transition-colors"
            title="Close"
          >
            <Settings className="w-4 h-4" />
          </motion.button>
        </div>
      </div>

      {/* Participants */}
      <div className="space-y-2 mb-4">
        <h4 className="text-xs font-medium text-muted-foreground">In Call</h4>
        
        {participants.length === 0 ? (
          <div className="text-center text-muted-foreground py-4">
            <Users className="w-6 h-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No one else in the call</p>
          </div>
        ) : (
          <div className="space-y-2">
            {participants.map((participant) => (
              <VoiceParticipant
                key={participant.id}
                participant={participant}
                audioLevel={participant.id === 'current-user' ? audioLevel : Math.random() * 100}
              />
            ))}
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center gap-3">
        {/* Mute/Unmute */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onToggleMute}
          className={cn(
            "p-3 rounded-full transition-colors",
            voiceCall.isMuted 
              ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" 
              : "bg-green-500 text-white hover:bg-green-600"
          )}
          title={voiceCall.isMuted ? "Unmute" : "Mute"}
        >
          {voiceCall.isMuted ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
        </motion.button>

        {/* Deafen/Undeafen */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => {}}
          className={cn(
            "p-3 rounded-full transition-colors",
            voiceCall.isDeafened 
              ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" 
              : "bg-muted text-muted-foreground hover:bg-accent"
          )}
          title={voiceCall.isDeafened ? "Undeafen" : "Deafen"}
        >
          {voiceCall.isDeafened ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
        </motion.button>

        {/* Settings */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => {}}
          className="p-3 rounded-full bg-muted text-muted-foreground hover:bg-accent transition-colors"
          title="Voice Settings"
        >
          <Settings className="w-5 h-5" />
        </motion.button>

        {/* End Call */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onEndCall}
          className="p-3 bg-destructive text-destructive-foreground rounded-full hover:bg-destructive/90 transition-colors"
          title="End Call"
        >
          <PhoneOff className="w-5 h-5" />
        </motion.button>
      </div>
    </div>
  );
}

interface VoiceParticipantProps {
  participant: any;
  audioLevel: number;
}

function VoiceParticipant({ participant, audioLevel }: VoiceParticipantProps) {
  const [isSpeaking, setIsSpeaking] = useState(false);

  useEffect(() => {
    setIsSpeaking(audioLevel > 30);
  }, [audioLevel]);

  return (
    <motion.div
      className={cn(
        "flex items-center gap-3 p-2 rounded-md transition-colors",
        isSpeaking && "bg-green-500/10 border border-green-500/20"
      )}
    >
      <div className={cn(
        "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors",
        isSpeaking 
          ? "bg-green-500 text-white" 
          : "bg-muted text-muted-foreground"
      )}>
        {participant.name.charAt(0).toUpperCase()}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium truncate">{participant.name}</span>
          {participant.isMuted && <MicOff className="w-3 h-3 text-destructive" />}
        </div>
        
        {/* Audio Level Indicator */}
        <div className="flex items-center gap-1 mt-1">
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className={cn(
                "w-1 h-2 rounded-full transition-colors",
                audioLevel > (i + 1) * 20 
                  ? "bg-green-500" 
                  : "bg-muted"
              )}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
}

// Voice Call Invitation Component
export function VoiceCallInvitation({ 
  fromUser, 
  onAccept, 
  onDecline 
}: {
  fromUser: string;
  onAccept: () => void;
  onDecline: () => void;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed top-4 right-4 bg-background border border-border rounded-lg shadow-lg p-4 z-50"
    >
      <div className="flex items-center gap-3 mb-3">
        <Phone className="w-5 h-5 text-green-500" />
        <div>
          <h4 className="text-sm font-medium">Incoming Voice Call</h4>
          <p className="text-xs text-muted-foreground">{fromUser} is calling you</p>
        </div>
      </div>
      
      <div className="flex gap-2">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onDecline}
          className="flex-1 px-3 py-2 bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 transition-colors"
        >
          Decline
        </motion.button>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onAccept}
          className="flex-1 px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
        >
          Accept
        </motion.button>
      </div>
    </motion.div>
  );
}
