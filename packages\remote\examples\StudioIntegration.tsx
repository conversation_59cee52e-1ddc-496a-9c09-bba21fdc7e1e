/**
 * Example integration of Remote Collaboration with KodeKilat Studio
 * This shows how to integrate the remote package with the main studio UI
 */

import React from 'react';
import { 
  RemoteCollaborationProvider,
  RemoteSessionManager,
  useRemoteEditorIntegration,
  useRemoteFileExplorer,
  useRemoteSidebar
} from '@kodekilat/remote';

// Main Studio App with Remote Collaboration
export function StudioWithCollaboration() {
  return (
    <RemoteCollaborationProvider enabled={true}>
      <div className="flex h-screen bg-background">
        {/* Sidebar with Remote Session Manager */}
        <Sidebar />
        
        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          <EditorArea />
        </div>
      </div>
    </RemoteCollaborationProvider>
  );
}

// Enhanced Sidebar with Remote Collaboration
function Sidebar() {
  const { 
    isConnected, 
    currentSession, 
    participants, 
    participantCount 
  } = useRemoteSidebar();

  return (
    <div className="w-64 bg-sidebar border-r border-border flex flex-col">
      {/* File Explorer */}
      <div className="flex-1 p-4">
        <h2 className="text-lg font-semibold mb-4">Explorer</h2>
        <FileExplorerWithCollaboration />
      </div>
      
      {/* Remote Collaboration Section */}
      <div className="border-t border-border">
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium">Collaboration</h3>
            {isConnected && (
              <div className="flex items-center gap-1 text-xs text-green-500">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                {participantCount} online
              </div>
            )}
          </div>
          
          <RemoteSessionManager className="h-64" />
        </div>
      </div>
    </div>
  );
}

// File Explorer with Collaboration Indicators
function FileExplorerWithCollaboration() {
  const { activeFiles, getFileEditors } = useRemoteFileExplorer();
  
  // Mock file tree
  const files = [
    { name: 'src', type: 'folder', children: [
      { name: 'App.tsx', type: 'file', path: '/src/App.tsx' },
      { name: 'index.tsx', type: 'file', path: '/src/index.tsx' },
      { name: 'components', type: 'folder', children: [
        { name: 'Button.tsx', type: 'file', path: '/src/components/Button.tsx' },
        { name: 'Modal.tsx', type: 'file', path: '/src/components/Modal.tsx' }
      ]}
    ]},
    { name: 'package.json', type: 'file', path: '/package.json' },
    { name: 'README.md', type: 'file', path: '/README.md' }
  ];

  const renderFileTree = (items: any[], level = 0) => {
    return items.map((item) => {
      const editors = item.type === 'file' ? getFileEditors(item.path) : [];
      const isActive = activeFiles.some(f => f.path === item.path && f.isActive);
      
      return (
        <div key={item.name} style={{ paddingLeft: level * 16 }}>
          <div className={`
            flex items-center gap-2 py-1 px-2 rounded hover:bg-accent cursor-pointer
            ${isActive ? 'bg-accent/50' : ''}
          `}>
            <span className="text-sm">{item.name}</span>
            
            {/* Collaboration Indicators */}
            {editors.length > 0 && (
              <div className="flex items-center gap-1">
                {editors.slice(0, 3).map((editor, i) => (
                  <div
                    key={editor.id}
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: editor.color }}
                    title={`${editor.name} is editing`}
                  />
                ))}
                {editors.length > 3 && (
                  <span className="text-xs text-muted-foreground">
                    +{editors.length - 3}
                  </span>
                )}
              </div>
            )}
          </div>
          
          {item.children && renderFileTree(item.children, level + 1)}
        </div>
      );
    });
  };

  return (
    <div className="space-y-1">
      {renderFileTree(files)}
    </div>
  );
}

// Enhanced Editor with Remote Collaboration
function EditorArea() {
  const {
    isConnected,
    handleTextChange,
    handleCursorChange,
    fileCursors,
    activeFile
  } = useRemoteEditorIntegration();

  return (
    <div className="flex-1 flex flex-col">
      {/* Editor Header */}
      <div className="h-12 border-b border-border flex items-center px-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">App.tsx</span>
          
          {/* Collaboration Status */}
          {isConnected && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span className="text-xs text-muted-foreground">Live</span>
            </div>
          )}
        </div>
        
        {/* Active Collaborators */}
        {isConnected && fileCursors[activeFile || ''] && (
          <div className="ml-auto flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Editing:</span>
            <div className="flex items-center gap-1">
              {Object.entries(fileCursors[activeFile || ''] || {}).map(([userId, cursor]) => (
                <div
                  key={userId}
                  className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium text-white"
                  style={{ backgroundColor: cursor.color }}
                  title={cursor.userName}
                >
                  {cursor.userName.charAt(0).toUpperCase()}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Monaco Editor */}
      <div className="flex-1 relative">
        <MonacoEditorWithCollaboration
          onTextChange={handleTextChange}
          onCursorChange={handleCursorChange}
          cursors={fileCursors[activeFile || ''] || {}}
        />
      </div>
    </div>
  );
}

// Monaco Editor with Collaboration Features
interface MonacoEditorWithCollaborationProps {
  onTextChange: (filePath: string, changes: any[], version: number) => void;
  onCursorChange: (filePath: string, position: any, selection: any) => void;
  cursors: Record<string, any>;
}

function MonacoEditorWithCollaboration({
  onTextChange,
  onCursorChange,
  cursors
}: MonacoEditorWithCollaborationProps) {
  // This would be the actual Monaco Editor integration
  // For demo purposes, we'll show a placeholder
  
  const handleChange = (value: string, event: any) => {
    if (event.changes && event.changes.length > 0) {
      onTextChange('current-file.tsx', event.changes, event.versionId);
    }
  };

  const handleCursorPositionChanged = (e: any) => {
    onCursorChange('current-file.tsx', e.position, e.selection);
  };

  return (
    <div className="w-full h-full bg-editor text-editor-foreground p-4 font-mono text-sm relative">
      {/* Placeholder Editor Content */}
      <div className="space-y-2">
        <div>import React from 'react';</div>
        <div>import { useState } from 'react';</div>
        <div></div>
        <div>function App() {`{`}</div>
        <div className="pl-4">const [count, setCount] = useState(0);</div>
        <div></div>
        <div className="pl-4">return (</div>
        <div className="pl-8">&lt;div className="app"&gt;</div>
        <div className="pl-12">&lt;h1&gt;KodeKilat Studio&lt;/h1&gt;</div>
        <div className="pl-12">&lt;p&gt;Count: {`{count}`}&lt;/p&gt;</div>
        <div className="pl-12">&lt;button onClick={() =&gt; setCount(count + 1)}&gt;</div>
        <div className="pl-16">Increment</div>
        <div className="pl-12">&lt;/button&gt;</div>
        <div className="pl-8">&lt;/div&gt;</div>
        <div className="pl-4">);</div>
        <div>{`}`}</div>
        <div></div>
        <div>export default App;</div>
      </div>
      
      {/* Collaboration Cursors Overlay */}
      <div className="absolute inset-0 pointer-events-none">
        {Object.entries(cursors).map(([userId, cursor]) => (
          <div
            key={userId}
            className="absolute w-0.5 h-5 animate-pulse"
            style={{
              backgroundColor: cursor.color,
              left: cursor.column * 8 + 16, // Approximate character width
              top: cursor.line * 20 + 16    // Approximate line height
            }}
          >
            {/* Cursor Label */}
            <div 
              className="absolute -top-6 left-0 px-2 py-1 text-xs text-white rounded whitespace-nowrap"
              style={{ backgroundColor: cursor.color }}
            >
              {cursor.userName}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Status Bar with Collaboration Info
export function StatusBarWithCollaboration() {
  const { isConnected, currentSession, participantCount } = useRemoteSidebar();
  
  return (
    <div className="h-6 bg-statusbar border-t border-border flex items-center justify-between px-4 text-xs">
      <div className="flex items-center gap-4">
        <span>Ready</span>
        <span>TypeScript React</span>
        <span>UTF-8</span>
      </div>
      
      {/* Collaboration Status */}
      {isConnected && currentSession && (
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
            <span>Live: {currentSession.name}</span>
          </div>
          <span>•</span>
          <span>{participantCount} participant{participantCount !== 1 ? 's' : ''}</span>
        </div>
      )}
    </div>
  );
}
