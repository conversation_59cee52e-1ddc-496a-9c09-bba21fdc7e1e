import { io, Socket } from 'socket.io-client';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type {
  ChatMessage,
  CreateSessionResponse,
  CursorPosition,
  JoinSessionResponse,
  RemoteSessionStore,
  TextChange,
  UserPermissions,
  WSMessage,
} from '../types';

// WebSocket connection
let socket: Socket | null = null;

const useRemoteSessionStore = create<RemoteSessionStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial State
    isConnected: false,
    connectionStatus: 'disconnected',
    currentSession: null,
    sessionId: null,
    userId: null,
    participants: [],
    onlineUsers: [],
    activeFile: null,
    fileCursors: {},
    fileVersions: {},
    chatMessages: [],
    voiceCall: {
      isActive: false,
      participants: [],
      isMuted: false,
      isDeafened: false,
    },
    screenShare: {
      isSharing: false,
    },
    permissions: {
      read: false,
      write: false,
      admin: false,
    },
    showChat: false,
    showParticipants: true,
    showVoiceControls: false,

    // Connection Actions
    connect: async (sessionId: string, userName: string, password?: string) => {
      try {
        set({ connectionStatus: 'connecting' });

        // Connect to WebSocket
        socket = io('ws://localhost:3001', {
          query: {
            sessionId,
            userId: `user-${Date.now()}`,
            userName,
          },
        });

        socket.on('connect', () => {
          console.log('🔗 Connected to collaboration server');
          set({
            isConnected: true,
            connectionStatus: 'connected',
            sessionId,
          });
        });

        socket.on('disconnect', () => {
          console.log('🔌 Disconnected from collaboration server');
          set({
            isConnected: false,
            connectionStatus: 'disconnected',
          });
        });

        // Handle WebSocket messages
        socket.on('message', (message: WSMessage) => {
          handleWebSocketMessage(message);
        });

        // Join session via API
        const response = await fetch('/api/remote/session/join', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sessionId, userName, password }),
        });

        const result: JoinSessionResponse = await response.json();

        if (result.success && result.sessionInfo) {
          set({
            userId: result.userId,
            currentSession: result.sessionInfo as any,
            participants: result.sessionInfo.participants,
          });

          // Get user permissions
          const permResponse = await fetch(
            `/api/remote/permissions?sessionId=${sessionId}&userId=${result.userId}`
          );
          const permData = await permResponse.json();

          set({ permissions: permData.permissions });
        } else {
          throw new Error(result.message || 'Failed to join session');
        }
      } catch (error) {
        console.error('Failed to connect to session:', error);
        set({
          connectionStatus: 'error',
          isConnected: false,
        });
        throw error;
      }
    },

    disconnect: () => {
      if (socket) {
        socket.disconnect();
        socket = null;
      }

      set({
        isConnected: false,
        connectionStatus: 'disconnected',
        currentSession: null,
        sessionId: null,
        userId: null,
        participants: [],
        onlineUsers: [],
        fileCursors: {},
        fileVersions: {},
        chatMessages: [],
      });
    },

    // Session Management
    createSession: async (projectPath: string, sessionName: string, isPublic = false) => {
      try {
        const response = await fetch('/api/remote/session/create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ projectPath, sessionName, isPublic }),
        });

        const result: CreateSessionResponse = await response.json();
        return result.sessionId;
      } catch (error) {
        console.error('Failed to create session:', error);
        throw error;
      }
    },

    joinSession: async (sessionId: string, userName: string, password?: string) => {
      await get().connect(sessionId, userName, password);
    },

    leaveSession: () => {
      const { sessionId, userId } = get();

      if (sessionId && userId) {
        fetch('/api/remote/session/leave', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sessionId, userId }),
        }).catch(console.error);
      }

      get().disconnect();
    },

    // File Operations
    sendTextChanges: (filePath: string, changes: TextChange[], version: number) => {
      const { sessionId, userId } = get();

      if (!socket || !sessionId || !userId) return;

      socket.emit('text_change', {
        sessionId,
        userId,
        filePath,
        changes,
        version,
      });

      // Update local version
      set(state => ({
        fileVersions: {
          ...state.fileVersions,
          [filePath]: version + 1,
        },
      }));
    },

    sendCursorPosition: (
      filePath: string,
      position: CursorPosition['position'],
      selection?: CursorPosition['selection']
    ) => {
      const { sessionId, userId } = get();

      if (!socket || !sessionId || !userId) return;

      socket.emit('cursor_move', {
        sessionId,
        userId,
        filePath,
        position,
        selection,
      });
    },

    requestFileSync: (filePath: string) => {
      const { sessionId, userId } = get();

      if (!socket || !sessionId || !userId) return;

      socket.emit('file_sync_request', {
        sessionId,
        userId,
        filePath,
      });
    },

    // Communication
    sendChatMessage: (message: string, type = 'text' as ChatMessage['type']) => {
      const { sessionId, userId, participants } = get();

      if (!socket || !sessionId || !userId) return;

      const user = participants.find(p => p.id === userId);
      const chatMessage: ChatMessage = {
        id: `msg-${Date.now()}`,
        userId,
        userName: user?.name || 'Unknown',
        message,
        timestamp: new Date().toISOString(),
        type,
      };

      socket.emit('chat_message', {
        sessionId,
        userId,
        message,
      });

      // Add to local messages immediately
      set(state => ({
        chatMessages: [...state.chatMessages, chatMessage],
      }));
    },

    startVoiceCall: () => {
      // WebRTC voice call implementation
      set(state => ({
        voiceCall: {
          ...state.voiceCall,
          isActive: true,
        },
        showVoiceControls: true,
      }));
    },

    endVoiceCall: () => {
      set(state => ({
        voiceCall: {
          ...state.voiceCall,
          isActive: false,
          participants: [],
        },
        showVoiceControls: false,
      }));
    },

    toggleMute: () => {
      set(state => ({
        voiceCall: {
          ...state.voiceCall,
          isMuted: !state.voiceCall.isMuted,
        },
      }));
    },

    startScreenShare: () => {
      set(state => ({
        screenShare: {
          ...state.screenShare,
          isSharing: true,
          sharerUserId: state.userId || undefined,
        },
      }));
    },

    stopScreenShare: () => {
      set(state => ({
        screenShare: {
          isSharing: false,
          sharerUserId: undefined,
        },
      }));
    },

    // UI Actions
    toggleChat: () => {
      set(state => ({ showChat: !state.showChat }));
    },

    toggleParticipants: () => {
      set(state => ({ showParticipants: !state.showParticipants }));
    },

    toggleVoiceControls: () => {
      set(state => ({ showVoiceControls: !state.showVoiceControls }));
    },

    // Permissions
    updateUserPermissions: async (targetUserId: string, permissions: UserPermissions) => {
      const { sessionId, userId } = get();

      if (!sessionId || !userId) return;

      try {
        const response = await fetch('/api/remote/permissions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sessionId, userId, targetUserId, permissions }),
        });

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.message);
        }
      } catch (error) {
        console.error('Failed to update permissions:', error);
        throw error;
      }
    },
  }))
);

// WebSocket message handler
function handleWebSocketMessage(message: WSMessage) {
  const store = useRemoteSessionStore.getState();

  switch (message.type) {
    case 'user_joined':
      // Handle user joined
      break;

    case 'user_left':
      // Handle user left
      break;

    case 'text_change':
      // Handle text changes from other users
      break;

    case 'cursor_move':
      // Handle cursor movements
      const { userId, filePath, position, selection } = message as any;
      useRemoteSessionStore.setState(state => ({
        fileCursors: {
          ...state.fileCursors,
          [filePath]: {
            ...state.fileCursors[filePath],
            [userId]: {
              position,
              selection,
              timestamp: message.timestamp,
            },
          },
        },
      }));
      break;

    case 'chat_message':
      // Handle chat messages
      const chatMsg = message as any;
      const participant = store.participants.find(p => p.id === chatMsg.userId);

      useRemoteSessionStore.setState(state => ({
        chatMessages: [
          ...state.chatMessages,
          {
            id: `msg-${Date.now()}`,
            userId: chatMsg.userId,
            userName: participant?.name || 'Unknown',
            message: chatMsg.message,
            timestamp: message.timestamp,
            type: 'text',
          },
        ],
      }));
      break;

    default:
      console.log('Unhandled WebSocket message:', message.type);
  }
}

export const useRemoteSession = () => useRemoteSessionStore();

// Selector hooks for specific parts of the state
export const useRemoteConnection = () =>
  useRemoteSessionStore(state => ({
    isConnected: state.isConnected,
    connectionStatus: state.connectionStatus,
    connect: state.connect,
    disconnect: state.disconnect,
  }));

export const useRemoteParticipants = () =>
  useRemoteSessionStore(state => ({
    participants: state.participants,
    onlineUsers: state.onlineUsers,
    currentSession: state.currentSession,
  }));

export const useRemoteChat = () =>
  useRemoteSessionStore(state => ({
    chatMessages: state.chatMessages,
    showChat: state.showChat,
    sendChatMessage: state.sendChatMessage,
    toggleChat: state.toggleChat,
  }));

export const useRemoteEditor = () =>
  useRemoteSessionStore(state => ({
    activeFile: state.activeFile,
    fileCursors: state.fileCursors,
    fileVersions: state.fileVersions,
    sendTextChanges: state.sendTextChanges,
    sendCursorPosition: state.sendCursorPosition,
    requestFileSync: state.requestFileSync,
  }));

export const useRemoteVoice = () =>
  useRemoteSessionStore(state => ({
    voiceCall: state.voiceCall,
    showVoiceControls: state.showVoiceControls,
    startVoiceCall: state.startVoiceCall,
    endVoiceCall: state.endVoiceCall,
    toggleMute: state.toggleMute,
    toggleVoiceControls: state.toggleVoiceControls,
  }));

export const useRemoteScreen = () =>
  useRemoteSessionStore(state => ({
    screenShare: state.screenShare,
    startScreenShare: state.startScreenShare,
    stopScreenShare: state.stopScreenShare,
  }));
