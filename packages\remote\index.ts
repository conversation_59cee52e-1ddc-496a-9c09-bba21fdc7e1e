// Remote Collaboration Package for KodeKilat Studio
// Export all components, hooks, and types

// Types
export * from './types';

// Hooks
export {
  useRemoteChat,
  useRemoteConnection,
  useRemoteEditor,
  useRemoteParticipants,
  useRemoteScreen,
  useRemoteSession,
  useRemoteVoice,
} from './hooks/useRemoteSession';

// Provider and Integration Hooks
export {
  RemoteCollaborationProvider,
  useRemoteCollaboration,
  useRemoteEditor as useRemoteEditorIntegration,
  useRemoteFileExplorer,
  useRemoteSidebar,
} from './components/RemoteCollaborationProvider';

// Components
export { CreateSessionDialog } from './components/CreateSessionDialog';
export { JoinSessionDialog } from './components/JoinSessionDialog';
export { ChatQuickActions, RemoteChat } from './components/RemoteChat';
export { RemoteSessionManager } from './components/RemoteSessionManager';
export { ScreenShare, ScreenShareRequest } from './components/ScreenShare';
export { VoiceCallInvitation, VoiceControls } from './components/VoiceControls';

// Utils
export { cn } from './utils/cn';
