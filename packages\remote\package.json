{"name": "@kodekilat/remote", "version": "1.0.0", "description": "Remote collaboration and live share components for KodeKilat Studio", "main": "index.ts", "types": "index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "socket.io-client": "^4.7.4", "simple-peer": "^9.11.1", "zustand": "^4.4.7", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/simple-peer": "^9.11.8", "typescript": "^5.3.2"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}