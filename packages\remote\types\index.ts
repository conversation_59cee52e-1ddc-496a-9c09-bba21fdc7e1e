// Remote Collaboration Types for KodeKilat Studio

export interface CollaborationSession {
  id: string;
  name: string;
  projectPath: string;
  isPublic: boolean;
  ownerId: string;
  createdAt: string;
  participants: Participant[];
  files: Record<string, FileState>;
  cursors: Record<string, Record<string, CursorPosition>>;
  permissions: Record<string, UserPermissions>;
}

export interface Participant {
  id: string;
  name: string;
  role: 'owner' | 'collaborator' | 'viewer';
  joinedAt: string;
  isOnline: boolean;
  avatar?: string;
  color?: string;
}

export interface FileState {
  content: string;
  version: number;
  history: FileChange[];
  locks: Record<string, FileLock>;
}

export interface FileChange {
  userId: string;
  changes: TextChange[];
  version: number;
  timestamp: string;
}

export interface TextChange {
  range: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  text: string;
  rangeLength: number;
}

export interface CursorPosition {
  position: {
    lineNumber: number;
    column: number;
  };
  selection?: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  timestamp: string;
}

export interface FileLock {
  userId: string;
  startLine: number;
  endLine: number;
  timestamp: string;
}

export interface UserPermissions {
  read: boolean;
  write: boolean;
  admin: boolean;
}

export interface ChatMessage {
  id: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: string;
  type: 'text' | 'system' | 'code' | 'file';
  metadata?: any;
}

export interface VoiceCallState {
  isActive: boolean;
  participants: string[];
  isMuted: boolean;
  isDeafened: boolean;
}

export interface ScreenShareState {
  isSharing: boolean;
  sharerUserId?: string;
  streamId?: string;
}

export interface WebRTCSignal {
  type: 'offer' | 'answer' | 'ice-candidate';
  data: any;
  fromUserId: string;
  toUserId: string;
}

export interface RemoteSessionState {
  // Connection
  isConnected: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  
  // Session
  currentSession: CollaborationSession | null;
  sessionId: string | null;
  userId: string | null;
  
  // Participants
  participants: Participant[];
  onlineUsers: string[];
  
  // Files & Editor
  activeFile: string | null;
  fileCursors: Record<string, Record<string, CursorPosition>>;
  fileVersions: Record<string, number>;
  
  // Communication
  chatMessages: ChatMessage[];
  voiceCall: VoiceCallState;
  screenShare: ScreenShareState;
  
  // Permissions
  permissions: UserPermissions;
  
  // UI State
  showChat: boolean;
  showParticipants: boolean;
  showVoiceControls: boolean;
}

export interface RemoteSessionActions {
  // Connection
  connect: (sessionId: string, userName: string, password?: string) => Promise<void>;
  disconnect: () => void;
  
  // Session Management
  createSession: (projectPath: string, sessionName: string, isPublic?: boolean) => Promise<string>;
  joinSession: (sessionId: string, userName: string, password?: string) => Promise<void>;
  leaveSession: () => void;
  
  // File Operations
  sendTextChanges: (filePath: string, changes: TextChange[], version: number) => void;
  sendCursorPosition: (filePath: string, position: CursorPosition['position'], selection?: CursorPosition['selection']) => void;
  requestFileSync: (filePath: string) => void;
  
  // Communication
  sendChatMessage: (message: string, type?: ChatMessage['type']) => void;
  startVoiceCall: () => void;
  endVoiceCall: () => void;
  toggleMute: () => void;
  startScreenShare: () => void;
  stopScreenShare: () => void;
  
  // UI Actions
  toggleChat: () => void;
  toggleParticipants: () => void;
  toggleVoiceControls: () => void;
  
  // Permissions
  updateUserPermissions: (targetUserId: string, permissions: UserPermissions) => void;
}

export type RemoteSessionStore = RemoteSessionState & RemoteSessionActions;

// WebSocket Message Types
export interface WSMessage {
  type: string;
  timestamp: string;
  [key: string]: any;
}

export interface WSConnectionMessage extends WSMessage {
  type: 'connection';
  status: 'connected' | 'disconnected';
  clientId: string;
}

export interface WSUserJoinedMessage extends WSMessage {
  type: 'user_joined';
  userId: string;
  clientId: string;
}

export interface WSUserLeftMessage extends WSMessage {
  type: 'user_left';
  userId: string;
  clientId: string;
}

export interface WSTextChangeMessage extends WSMessage {
  type: 'text_change';
  userId: string;
  filePath: string;
  changes: TextChange[];
  version: number;
}

export interface WSCursorMoveMessage extends WSMessage {
  type: 'cursor_move';
  userId: string;
  filePath: string;
  position: CursorPosition['position'];
  selection?: CursorPosition['selection'];
}

export interface WSChatMessage extends WSMessage {
  type: 'chat_message';
  userId: string;
  message: string;
}

export interface WSVoiceCallMessage extends WSMessage {
  type: 'voice_call';
  fromUserId: string;
  signalType: string;
  signalData: any;
}

export interface WSScreenShareMessage extends WSMessage {
  type: 'screen_share';
  userId: string;
  action: 'start' | 'stop' | 'frame';
  frameData?: any;
}

// API Response Types
export interface CreateSessionResponse {
  sessionId: string;
  sessionName: string;
  ownerId: string;
  joinUrl: string;
  shareCode: string;
}

export interface JoinSessionResponse {
  success: boolean;
  userId?: string;
  sessionInfo?: {
    id: string;
    name: string;
    participants: Participant[];
    projectPath: string;
  };
  message?: string;
}

export interface SessionInfoResponse {
  id: string;
  name: string;
  projectPath: string;
  isPublic: boolean;
  participants: Participant[];
  createdAt: string;
}
