import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { SearchAddon } from 'xterm-addon-search';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  X, 
  Square, 
  Play, 
  RotateCcw, 
  Search, 
  Settings,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { Terminal as TerminalType, TerminalCommand } from '../../shared/types';

interface TerminalComponentProps {
  className?: string;
  onTerminalCreate?: (terminal: TerminalType) => void;
  onTerminalClose?: (terminalId: string) => void;
}

interface TerminalTab {
  id: string;
  name: string;
  cwd: string;
  terminal: Terminal;
  active: boolean;
  pid?: number;
  createdAt: string;
}

export function TerminalComponent({ 
  className, 
  onTerminalCreate, 
  onTerminalClose 
}: TerminalComponentProps) {
  const [terminals, setTerminals] = useState<TerminalTab[]>([]);
  const [activeTerminal, setActiveTerminal] = useState<string | null>(null);
  const [isMaximized, setIsMaximized] = useState(false);
  const terminalRefs = useRef<Record<string, HTMLDivElement>>({});
  const fitAddons = useRef<Record<string, FitAddon>>({});

  useEffect(() => {
    // Create initial terminal
    if (terminals.length === 0) {
      createNewTerminal();
    }

    // Cleanup on unmount
    return () => {
      terminals.forEach(term => {
        term.terminal.dispose();
      });
    };
  }, []);

  useEffect(() => {
    // Fit terminals when component resizes
    const handleResize = () => {
      Object.values(fitAddons.current).forEach(fitAddon => {
        try {
          fitAddon.fit();
        } catch (error) {
          console.warn('Error fitting terminal:', error);
        }
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const createNewTerminal = async () => {
    const terminalId = `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create xterm instance
    const terminal = new Terminal({
      theme: {
        background: '#1e1e1e',
        foreground: '#ffffff',
        cursor: '#FACC15',
        cursorAccent: '#1e1e1e',
        selection: '#264f78',
        black: '#000000',
        red: '#cd3131',
        green: '#0dbc79',
        yellow: '#e5e510',
        blue: '#2472c8',
        magenta: '#bc3fbc',
        cyan: '#11a8cd',
        white: '#e5e5e5',
        brightBlack: '#666666',
        brightRed: '#f14c4c',
        brightGreen: '#23d18b',
        brightYellow: '#f5f543',
        brightBlue: '#3b8eea',
        brightMagenta: '#d670d6',
        brightCyan: '#29b8db',
        brightWhite: '#e5e5e5'
      },
      fontFamily: 'JetBrains Mono, Fira Code, Consolas, monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      scrollback: 1000,
      tabStopWidth: 4,
      allowTransparency: false
    });

    // Add addons
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();
    const searchAddon = new SearchAddon();

    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);
    terminal.loadAddon(searchAddon);

    fitAddons.current[terminalId] = fitAddon;

    // Get current working directory
    const cwd = await getCurrentWorkingDirectory();

    const newTerminal: TerminalTab = {
      id: terminalId,
      name: `Terminal ${terminals.length + 1}`,
      cwd,
      terminal,
      active: true,
      createdAt: new Date().toISOString()
    };

    // Add terminal to state
    setTerminals(prev => [
      ...prev.map(t => ({ ...t, active: false })),
      newTerminal
    ]);
    setActiveTerminal(terminalId);

    // Notify parent component
    onTerminalCreate?.({
      id: terminalId,
      name: newTerminal.name,
      cwd,
      shell: getDefaultShell(),
      active: true,
      createdAt: newTerminal.createdAt
    });

    // Setup terminal event handlers
    setupTerminalHandlers(terminal, terminalId);
  };

  const getCurrentWorkingDirectory = async (): Promise<string> => {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        // Get workspace path from electron store
        const workspace = await window.electronAPI.store.get('lastWorkspace');
        return workspace?.path || process.cwd();
      }
    } catch (error) {
      console.warn('Error getting workspace path:', error);
    }
    return process.cwd();
  };

  const getDefaultShell = (): string => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      const platform = window.electronAPI.platform;
      switch (platform) {
        case 'win32':
          return 'powershell.exe';
        case 'darwin':
          return '/bin/zsh';
        default:
          return '/bin/bash';
      }
    }
    return '/bin/bash';
  };

  const setupTerminalHandlers = (terminal: Terminal, terminalId: string) => {
    // Handle terminal data (user input)
    terminal.onData(data => {
      // Send data to backend terminal process
      sendToTerminalProcess(terminalId, data);
    });

    // Handle terminal resize
    terminal.onResize(({ cols, rows }) => {
      // Notify backend about terminal resize
      resizeTerminalProcess(terminalId, cols, rows);
    });

    // Handle terminal title change
    terminal.onTitleChange(title => {
      setTerminals(prev => prev.map(term => 
        term.id === terminalId 
          ? { ...term, name: title || term.name }
          : term
      ));
    });
  };

  const sendToTerminalProcess = async (terminalId: string, data: string) => {
    try {
      // Call API to send data to terminal process
      const response = await fetch('/api/terminal/input', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ terminalId, data })
      });

      if (!response.ok) {
        console.error('Error sending terminal input:', response.statusText);
      }
    } catch (error) {
      console.error('Error sending terminal input:', error);
    }
  };

  const resizeTerminalProcess = async (terminalId: string, cols: number, rows: number) => {
    try {
      // Call API to resize terminal process
      const response = await fetch('/api/terminal/resize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ terminalId, cols, rows })
      });

      if (!response.ok) {
        console.error('Error resizing terminal:', response.statusText);
      }
    } catch (error) {
      console.error('Error resizing terminal:', error);
    }
  };

  const closeTerminal = (terminalId: string) => {
    const terminal = terminals.find(t => t.id === terminalId);
    if (terminal) {
      terminal.terminal.dispose();
      delete terminalRefs.current[terminalId];
      delete fitAddons.current[terminalId];

      const newTerminals = terminals.filter(t => t.id !== terminalId);
      setTerminals(newTerminals);

      // Switch to another terminal or close if last one
      if (activeTerminal === terminalId) {
        if (newTerminals.length > 0) {
          setActiveTerminal(newTerminals[newTerminals.length - 1].id);
        } else {
          setActiveTerminal(null);
        }
      }

      onTerminalClose?.(terminalId);
    }
  };

  const switchTerminal = (terminalId: string) => {
    setTerminals(prev => prev.map(term => ({
      ...term,
      active: term.id === terminalId
    })));
    setActiveTerminal(terminalId);

    // Fit terminal when switching
    setTimeout(() => {
      const fitAddon = fitAddons.current[terminalId];
      if (fitAddon) {
        try {
          fitAddon.fit();
        } catch (error) {
          console.warn('Error fitting terminal on switch:', error);
        }
      }
    }, 100);
  };

  const clearTerminal = () => {
    const terminal = terminals.find(t => t.id === activeTerminal);
    if (terminal) {
      terminal.terminal.clear();
    }
  };

  const killTerminalProcess = async (terminalId: string) => {
    try {
      const response = await fetch('/api/terminal/kill', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ terminalId })
      });

      if (!response.ok) {
        console.error('Error killing terminal process:', response.statusText);
      }
    } catch (error) {
      console.error('Error killing terminal process:', error);
    }
  };

  // Mount terminal to DOM when ref is available
  useEffect(() => {
    terminals.forEach(terminalTab => {
      const ref = terminalRefs.current[terminalTab.id];
      if (ref && !terminalTab.terminal.element) {
        terminalTab.terminal.open(ref);
        
        // Fit terminal after mounting
        setTimeout(() => {
          const fitAddon = fitAddons.current[terminalTab.id];
          if (fitAddon) {
            try {
              fitAddon.fit();
            } catch (error) {
              console.warn('Error fitting terminal on mount:', error);
            }
          }
        }, 100);
      }
    });
  }, [terminals]);

  if (terminals.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full bg-background text-muted-foreground ${className}`}>
        <div className="text-center">
          <div className="mb-4">
            <Square className="w-12 h-12 mx-auto text-primary/30" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No Terminal</h3>
          <p className="text-sm mb-4">Create a new terminal to start</p>
          <button
            onClick={createNewTerminal}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <Plus className="w-4 h-4 inline mr-2" />
            New Terminal
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      className={`flex flex-col h-full bg-background ${className}`}
      animate={{ height: isMaximized ? '100vh' : 'auto' }}
      transition={{ duration: 0.3 }}
    >
      {/* Terminal Tab Bar */}
      <div className="flex items-center bg-muted border-b border-border px-2 py-1">
        <div className="flex items-center space-x-1 flex-1">
          {/* Terminal Tabs */}
          <div className="flex overflow-x-auto">
            <AnimatePresence>
              {terminals.map((terminal) => (
                <motion.button
                  key={terminal.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  onClick={() => switchTerminal(terminal.id)}
                  className={`flex items-center px-3 py-1.5 text-sm rounded-sm transition-colors ${
                    terminal.active 
                      ? 'bg-background text-foreground' 
                      : 'hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <span className="truncate max-w-32">{terminal.name}</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      closeTerminal(terminal.id);
                    }}
                    className="ml-2 p-0.5 hover:bg-accent rounded"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </motion.button>
              ))}
            </AnimatePresence>
          </div>

          {/* Add Terminal Button */}
          <button
            onClick={createNewTerminal}
            className="p-1.5 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
            title="New Terminal"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>

        {/* Terminal Controls */}
        <div className="flex items-center space-x-1">
          <button
            onClick={clearTerminal}
            className="p-1.5 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
            title="Clear Terminal"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => activeTerminal && killTerminalProcess(activeTerminal)}
            className="p-1.5 hover:bg-destructive hover:text-destructive-foreground rounded-sm transition-colors"
            title="Kill Terminal Process"
          >
            <Square className="w-4 h-4" />
          </button>

          <button
            onClick={() => setIsMaximized(!isMaximized)}
            className="p-1.5 hover:bg-accent hover:text-accent-foreground rounded-sm transition-colors"
            title={isMaximized ? "Restore" : "Maximize"}
          >
            {isMaximized ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Terminal Content */}
      <div className="flex-1 relative">
        {terminals.map((terminal) => (
          <div
            key={terminal.id}
            ref={(ref) => {
              if (ref) {
                terminalRefs.current[terminal.id] = ref;
              }
            }}
            className={`absolute inset-0 ${terminal.active ? 'block' : 'hidden'}`}
            style={{ padding: '8px' }}
          />
        ))}
      </div>
    </motion.div>
  );
}

export default TerminalComponent;
