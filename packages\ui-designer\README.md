# @kodekilat/ui-designer

Visual UI Designer untuk KodeKilat Studio - sistem drag & drop interface builder dengan live preview dan code generation.

## ✨ Fitur Utama

- **🎨 Visual Design Canvas** - Area design dengan drag & drop support
- **🧩 Element Palette** - Library komponen yang lengkap dan terorganisir
- **⚙️ Property Panel** - Editor visual untuk properties dan styling
- **👁️ Live Preview** - Preview real-time dengan responsive design
- **💻 Code Generator** - Generate React, HTML, Vue, dan Angular code
- **📱 Responsive Design** - Support multiple breakpoints dan device presets
- **🔄 Undo/Redo** - History management untuk design operations
- **📋 Clipboard** - Copy/paste elements dengan styling
- **🎯 Element Selection** - Advanced selection dengan keyboard shortcuts

## 🚀 Quick Start

```tsx
import { UIDesigner } from '@kodekilat/ui-designer';

function App() {
  const handleSave = (design) => {
    console.log('Design saved:', design);
  };

  return (
    <div style={{ height: '100vh' }}>
      <UIDesigner onSave={handleSave} />
    </div>
  );
}
```

## 📦 Komponen Utama

### UIDesigner
Komponen utama yang mengintegrasikan semua fitur UI designer.

```tsx
<UIDesigner
  className="custom-designer"
  onSave={(design) => console.log(design)}
  onLoad={() => loadDesignFromStorage()}
  initialDesign={existingDesign}
/>
```

### ElementPalette
Panel elemen dengan kategori dan search functionality.

```tsx
<ElementPalette designer={designerInstance} />
```

### DesignCanvas
Area design dengan drag & drop dan element manipulation.

```tsx
<DesignCanvas designer={designerInstance} />
```

### PropertyPanel
Panel untuk editing properties dan styling elements.

```tsx
<PropertyPanel 
  designer={designerInstance}
  selectedElement={selectedElement}
/>
```

### LivePreview
Preview real-time dengan device presets.

```tsx
<LivePreview elements={designElements} />
```

### CodeGenerator
Generator kode untuk berbagai framework.

```tsx
<CodeGenerator elements={designElements} />
```

## 🎯 Hook: useUIDesigner

Hook utama untuk state management UI designer.

```tsx
const designer = useUIDesigner();

// Element operations
designer.addElement('div', { x: 100, y: 100 });
designer.removeElement(elementId);
designer.updateElement(elementId, { name: 'New Name' });

// Selection
designer.selectElement(elementId);
designer.selectMultiple([id1, id2]);

// History
designer.undo();
designer.redo();

// Canvas controls
designer.setZoom(1.5);
designer.setPan({ x: 0, y: 0 });
designer.setDevice('mobile');

// Import/Export
const design = designer.exportDesign();
designer.importDesign(design);
```

## 🎨 Element Types

### Layout Elements
- `div` - Container
- `Flex` - Flexbox container
- `Grid` - Grid container
- `section`, `article`, `header`, `footer`, `nav`, `main`, `aside`

### Typography
- `h1`, `h2`, `h3`, `h4`, `h5`, `h6` - Headings
- `p` - Paragraph
- `span` - Inline text

### Form Elements
- `input` - Input field
- `textarea` - Text area
- `select` - Select dropdown
- `button` - Button

### Media
- `img` - Image

### Components
- `Card` - Card component
- `Button` - Styled button

## 🎛️ Properties

Setiap element memiliki properties yang dapat diedit:

### General Properties
- `name` - Element name
- `id` - Element ID
- `className` - CSS classes
- `children` - Text content

### Layout Properties
- `width`, `height` - Dimensions
- `left`, `top` - Position
- `display` - Display type
- `flexDirection`, `justifyContent`, `alignItems` - Flexbox
- `gridTemplateColumns`, `gridTemplateRows` - Grid

### Typography Properties
- `fontSize`, `fontWeight`, `fontFamily`
- `lineHeight`, `textAlign`, `color`
- `textDecoration`

### Appearance Properties
- `backgroundColor`, `border`, `borderRadius`
- `boxShadow`, `opacity`, `overflow`

### Spacing Properties
- `margin*`, `padding*` - Spacing values

## 📱 Responsive Design

Support untuk multiple breakpoints:

```tsx
const breakpoints = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1440, height: 900 },
  wide: { width: 1920, height: 1080 }
};
```

## 💻 Code Generation

Generate kode untuk berbagai framework:

### React/JSX
```jsx
import React from 'react';

export default function GeneratedComponent() {
  return (
    <div className="generated-component">
      {/* Generated elements */}
    </div>
  );
}
```

### HTML
```html
<!DOCTYPE html>
<html>
<head>
  <title>Generated Page</title>
</head>
<body>
  <!-- Generated elements -->
</body>
</html>
```

### Vue
```vue
<template>
  <div class="generated-component">
    <!-- Generated elements -->
  </div>
</template>
```

### Angular
```typescript
@Component({
  selector: 'app-generated',
  template: `<!-- Generated elements -->`
})
export class GeneratedComponent {}
```

## ⌨️ Keyboard Shortcuts

- `Ctrl+Z` - Undo
- `Ctrl+Y` - Redo
- `Ctrl+C` - Copy element
- `Ctrl+V` - Paste element
- `Ctrl+S` - Save design
- `Delete` - Delete selected element
- `Escape` - Deselect all

## 🎨 Styling

Package ini menggunakan CSS variables untuk theming:

```css
:root {
  --primary: #3b82f6;
  --secondary: #f1f5f9;
  --accent: #e2e8f0;
  --border: #e5e7eb;
  --muted-foreground: #6b7280;
}
```

## 📄 License

MIT License - lihat file LICENSE untuk detail lengkap.
