import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Code, 
  Copy, 
  Download, 
  FileText, 
  Braces,
  Check
} from 'lucide-react';
import { UIElement } from '../types';

interface CodeGeneratorProps {
  elements: UIElement[];
  className?: string;
}

type CodeFormat = 'react' | 'html' | 'vue' | 'angular';

export function CodeGenerator({ elements, className = '' }: CodeGeneratorProps) {
  const [selectedFormat, setSelectedFormat] = useState<CodeFormat>('react');
  const [copied, setCopied] = useState(false);

  // Generate React/JSX code
  const generateReactCode = (elements: UIElement[]): string => {
    const renderElement = (element: UIElement, depth = 0): string => {
      const indent = '  '.repeat(depth + 1);
      const childIndent = '  '.repeat(depth + 2);
      
      // Convert styles to React style object
      const styleEntries = Object.entries(element.styles || {});
      const styleString = styleEntries.length > 0 
        ? `style={{${styleEntries.map(([key, value]) => 
            `${key}: ${typeof value === 'string' ? `"${value}"` : value}`
          ).join(', ')}}}`
        : '';

      // Convert props
      const propEntries = Object.entries(element.props || {})
        .filter(([key]) => key !== 'children');
      const propsString = propEntries.length > 0 
        ? propEntries.map(([key, value]) => 
            `${key}="${value}"`
          ).join(' ')
        : '';

      const allProps = [propsString, styleString].filter(Boolean).join(' ');
      const textContent = element.props?.children || '';
      const hasChildren = element.children.length > 0 || textContent;

      // Handle different element types
      let elementCode = '';
      switch (element.type) {
        case 'img':
          elementCode = `${indent}<img ${allProps} />`;
          break;
        case 'input':
          elementCode = `${indent}<input ${allProps} />`;
          break;
        case 'Flex':
          elementCode = hasChildren 
            ? `${indent}<div ${allProps}>\n${element.children.map(child => renderElement(child, depth + 1)).join('\n')}\n${textContent ? `${childIndent}${textContent}\n` : ''}${indent}</div>`
            : `${indent}<div ${allProps} />`;
          break;
        case 'Grid':
          elementCode = hasChildren 
            ? `${indent}<div ${allProps}>\n${element.children.map(child => renderElement(child, depth + 1)).join('\n')}\n${textContent ? `${childIndent}${textContent}\n` : ''}${indent}</div>`
            : `${indent}<div ${allProps} />`;
          break;
        case 'Card':
          elementCode = hasChildren 
            ? `${indent}<div className="card" ${allProps}>\n${element.children.map(child => renderElement(child, depth + 1)).join('\n')}\n${textContent ? `${childIndent}${textContent}\n` : ''}${indent}</div>`
            : `${indent}<div className="card" ${allProps} />`;
          break;
        case 'Button':
          elementCode = hasChildren 
            ? `${indent}<button className="ui-button" ${allProps}>\n${textContent ? `${childIndent}${textContent}\n` : ''}${element.children.map(child => renderElement(child, depth + 1)).join('\n')}\n${indent}</button>`
            : `${indent}<button className="ui-button" ${allProps}>${textContent}</button>`;
          break;
        default:
          const tag = element.type;
          if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div', 'section', 'button', 'textarea', 'select'].includes(tag)) {
            if (tag === 'select') {
              const options = Array.isArray(element.props?.children) 
                ? element.props.children.map((opt: any) => 
                    `${childIndent}<option value="${opt.props?.value || ''}">${opt.props?.children || ''}</option>`
                  ).join('\n')
                : '';
              elementCode = `${indent}<${tag} ${allProps}>\n${options}\n${indent}</${tag}>`;
            } else {
              elementCode = hasChildren 
                ? `${indent}<${tag} ${allProps}>\n${textContent ? `${childIndent}${textContent}\n` : ''}${element.children.map(child => renderElement(child, depth + 1)).join('\n')}\n${indent}</${tag}>`
                : `${indent}<${tag} ${allProps}>${textContent}</${tag}>`;
            }
          } else {
            elementCode = hasChildren 
              ? `${indent}<div ${allProps}>\n${textContent ? `${childIndent}${textContent}\n` : ''}${element.children.map(child => renderElement(child, depth + 1)).join('\n')}\n${indent}</div>`
              : `${indent}<div ${allProps}>${textContent}</div>`;
          }
      }

      return elementCode;
    };

    const componentsCode = elements.map(element => renderElement(element)).join('\n');

    return `import React from 'react';

export default function GeneratedComponent() {
  return (
    <div className="generated-component">
${componentsCode}
    </div>
  );
}`;
  };

  // Generate HTML code
  const generateHTMLCode = (elements: UIElement[]): string => {
    const renderElement = (element: UIElement, depth = 0): string => {
      const indent = '  '.repeat(depth + 2);
      
      const styles = Object.entries(element.styles || {})
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}${typeof value === 'number' && !['opacity', 'z-index', 'font-weight'].includes(key.replace(/([A-Z])/g, '-$1').toLowerCase()) ? 'px' : ''}`)
        .join('; ');

      const props = Object.entries(element.props || {})
        .filter(([key]) => key !== 'children')
        .map(([key, value]) => `${key}="${value}"`)
        .join(' ');

      const styleAttr = styles ? `style="${styles}"` : '';
      const allAttrs = [props, styleAttr].filter(Boolean).join(' ');
      const textContent = element.props?.children || '';

      // Handle different element types
      switch (element.type) {
        case 'img':
          return `${indent}<img ${allAttrs} />`;
        case 'input':
          return `${indent}<input ${allAttrs} />`;
        case 'textarea':
          return `${indent}<textarea ${allAttrs}>${textContent}</textarea>`;
        case 'select':
          const options = Array.isArray(element.props?.children) 
            ? element.props.children.map((opt: any) => `<option value="${opt.props?.value || ''}">${opt.props?.children || ''}</option>`).join('')
            : '';
          return `${indent}<select ${allAttrs}>${options}</select>`;
        case 'Flex':
          return `${indent}<div ${allAttrs} style="display: flex; ${styles}">${textContent}${element.children.map(child => renderElement(child, depth + 1)).join('')}</div>`;
        case 'Grid':
          return `${indent}<div ${allAttrs} style="display: grid; ${styles}">${textContent}${element.children.map(child => renderElement(child, depth + 1)).join('')}</div>`;
        case 'Card':
          return `${indent}<div class="card" ${allAttrs}>${textContent}${element.children.map(child => renderElement(child, depth + 1)).join('')}</div>`;
        case 'Button':
          return `${indent}<button class="ui-button" ${allAttrs}>${textContent}${element.children.map(child => renderElement(child, depth + 1)).join('')}</button>`;
        default:
          const tag = element.type;
          if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div', 'section', 'button'].includes(tag)) {
            return `${indent}<${tag} ${allAttrs}>${textContent}${element.children.map(child => renderElement(child, depth + 1)).join('')}</${tag}>`;
          }
          return `${indent}<div ${allAttrs}>${textContent}${element.children.map(child => renderElement(child, depth + 1)).join('')}</div>`;
      }
    };

    const elementsHTML = elements.map(element => renderElement(element)).join('\n');

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Page</title>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .ui-button {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            cursor: pointer;
        }
    </style>
</head>
<body>
${elementsHTML}
</body>
</html>`;
  };

  // Generate Vue code
  const generateVueCode = (elements: UIElement[]): string => {
    const renderElement = (element: UIElement, depth = 0): string => {
      const indent = '  '.repeat(depth + 2);
      
      const styles = Object.entries(element.styles || {})
        .map(([key, value]) => `'${key}': '${value}${typeof value === 'number' && !['opacity', 'zIndex', 'fontWeight'].includes(key) ? 'px' : ''}'`)
        .join(', ');

      const styleBinding = styles ? `:style="{${styles}}"` : '';
      const textContent = element.props?.children || '';

      switch (element.type) {
        case 'img':
          return `${indent}<img ${styleBinding} />`;
        case 'input':
          return `${indent}<input ${styleBinding} />`;
        default:
          const tag = element.type === 'Flex' ? 'div' : element.type === 'Grid' ? 'div' : element.type.toLowerCase();
          return `${indent}<${tag} ${styleBinding}>${textContent}${element.children.map(child => renderElement(child, depth + 1)).join('')}</${tag}>`;
      }
    };

    const template = elements.map(element => renderElement(element)).join('\n');

    return `<template>
  <div class="generated-component">
${template}
  </div>
</template>

<script>
export default {
  name: 'GeneratedComponent'
}
</script>

<style scoped>
.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>`;
  };

  // Generate Angular code
  const generateAngularCode = (elements: UIElement[]): string => {
    const renderElement = (element: UIElement, depth = 0): string => {
      const indent = '  '.repeat(depth);
      
      const styles = Object.entries(element.styles || {})
        .map(([key, value]) => `'${key}': '${value}${typeof value === 'number' && !['opacity', 'zIndex', 'fontWeight'].includes(key) ? 'px' : ''}'`)
        .join(', ');

      const styleBinding = styles ? `[ngStyle]="{${styles}}"` : '';
      const textContent = element.props?.children || '';

      switch (element.type) {
        case 'img':
          return `${indent}<img ${styleBinding} />`;
        case 'input':
          return `${indent}<input ${styleBinding} />`;
        default:
          const tag = element.type === 'Flex' ? 'div' : element.type === 'Grid' ? 'div' : element.type.toLowerCase();
          return `${indent}<${tag} ${styleBinding}>${textContent}${element.children.map(child => renderElement(child, depth + 1)).join('')}</${tag}>`;
      }
    };

    const template = elements.map(element => renderElement(element)).join('\n');

    return `import { Component } from '@angular/core';

@Component({
  selector: 'app-generated',
  template: \`
    <div class="generated-component">
${template}
    </div>
  \`,
  styles: [\`
    .card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  \`]
})
export class GeneratedComponent {}`;
  };

  // Get generated code based on format
  const generatedCode = useMemo(() => {
    switch (selectedFormat) {
      case 'react':
        return generateReactCode(elements);
      case 'html':
        return generateHTMLCode(elements);
      case 'vue':
        return generateVueCode(elements);
      case 'angular':
        return generateAngularCode(elements);
      default:
        return generateReactCode(elements);
    }
  }, [elements, selectedFormat]);

  // Copy to clipboard
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(generatedCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  // Download as file
  const handleDownload = () => {
    const extensions = {
      react: 'jsx',
      html: 'html',
      vue: 'vue',
      angular: 'ts'
    };
    
    const extension = extensions[selectedFormat];
    const filename = `generated-component.${extension}`;
    
    const blob = new Blob([generatedCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-border bg-secondary/50">
        <div className="flex items-center space-x-2">
          <Code size={16} className="text-muted-foreground" />
          <span className="text-sm font-medium">Code Generator</span>
          <span className="text-xs text-muted-foreground">
            ({elements.length} elements)
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* Format Selection */}
          <div className="flex items-center space-x-1 bg-background rounded-md p-1">
            {(['react', 'html', 'vue', 'angular'] as CodeFormat[]).map(format => (
              <button
                key={format}
                onClick={() => setSelectedFormat(format)}
                className={`px-3 py-1 text-xs rounded transition-colors ${
                  selectedFormat === format 
                    ? 'bg-primary text-primary-foreground' 
                    : 'hover:bg-accent'
                }`}
              >
                {format.charAt(0).toUpperCase() + format.slice(1)}
              </button>
            ))}
          </div>

          <div className="w-px h-6 bg-border" />

          {/* Actions */}
          <button
            onClick={handleCopy}
            className="flex items-center space-x-1 px-3 py-1 text-sm bg-background border border-border rounded hover:bg-accent transition-colors"
          >
            {copied ? <Check size={14} /> : <Copy size={14} />}
            <span>{copied ? 'Copied!' : 'Copy'}</span>
          </button>
          
          <button
            onClick={handleDownload}
            className="flex items-center space-x-1 px-3 py-1 text-sm bg-background border border-border rounded hover:bg-accent transition-colors"
          >
            <Download size={14} />
            <span>Download</span>
          </button>
        </div>
      </div>

      {/* Code Display */}
      <div className="flex-1 overflow-hidden">
        <pre className="h-full overflow-auto p-4 text-sm font-mono bg-gray-50 dark:bg-gray-900">
          <code className="text-gray-800 dark:text-gray-200">
            {generatedCode}
          </code>
        </pre>
      </div>

      {/* Stats */}
      <div className="p-3 border-t border-border bg-secondary/50 text-center">
        <div className="text-xs text-muted-foreground">
          {generatedCode.split('\n').length} lines • {generatedCode.length} characters
        </div>
      </div>
    </div>
  );
}
