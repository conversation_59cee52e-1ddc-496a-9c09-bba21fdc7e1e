import React, { useRef, useCallback, useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Move, 
  RotateCw, 
  Square, 
  Circle,
  MousePointer,
  Trash2,
  Co<PERSON>,
  Eye,
  EyeOff
} from 'lucide-react';
import { UIElement, UseUIDesignerResult } from '../types';

interface DesignCanvasProps {
  designer: UseUIDesignerResult;
}

interface DragState {
  isDragging: boolean;
  dragType: 'element' | 'canvas' | 'resize';
  startPosition: { x: number; y: number };
  elementId?: string;
  resizeHandle?: string;
}

export function DesignCanvas({ designer }: DesignCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    dragType: 'canvas',
    startPosition: { x: 0, y: 0 }
  });
  const [showElementBounds, setShowElementBounds] = useState(true);

  // Handle canvas drag (pan)
  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      setDragState({
        isDragging: true,
        dragType: 'canvas',
        startPosition: { x: e.clientX, y: e.clientY }
      });
      designer.selectElement(null);
    }
  }, [designer]);

  // Handle element drag
  const handleElementMouseDown = useCallback((e: React.MouseEvent, elementId: string) => {
    e.stopPropagation();
    setDragState({
      isDragging: true,
      dragType: 'element',
      startPosition: { x: e.clientX, y: e.clientY },
      elementId
    });
    designer.selectElement(elementId);
  }, [designer]);

  // Handle mouse move
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragState.isDragging) return;

    const deltaX = e.clientX - dragState.startPosition.x;
    const deltaY = e.clientY - dragState.startPosition.y;

    if (dragState.dragType === 'canvas') {
      // Pan canvas
      const newPan = {
        x: designer.state.canvas.pan.x + deltaX,
        y: designer.state.canvas.pan.y + deltaY
      };
      designer.setPan(newPan);
      setDragState(prev => ({
        ...prev,
        startPosition: { x: e.clientX, y: e.clientY }
      }));
    } else if (dragState.dragType === 'element' && dragState.elementId) {
      // Move element
      const element = designer.getElementById(dragState.elementId);
      if (element) {
        const newPosition = {
          x: element.position.x + deltaX / designer.state.canvas.zoom,
          y: element.position.y + deltaY / designer.state.canvas.zoom
        };
        designer.moveElement(dragState.elementId, newPosition);
        setDragState(prev => ({
          ...prev,
          startPosition: { x: e.clientX, y: e.clientY }
        }));
      }
    }
  }, [dragState, designer]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    setDragState({
      isDragging: false,
      dragType: 'canvas',
      startPosition: { x: 0, y: 0 }
    });
  }, []);

  // Handle drop from element palette
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    
    try {
      const data = JSON.parse(e.dataTransfer.getData('application/json'));
      if (data.type === 'element') {
        const rect = canvasRef.current?.getBoundingClientRect();
        if (rect) {
          const x = (e.clientX - rect.left - designer.state.canvas.pan.x) / designer.state.canvas.zoom;
          const y = (e.clientY - rect.top - designer.state.canvas.pan.y) / designer.state.canvas.zoom;
          
          const elementId = designer.addElement(data.elementType, { x, y }, data.defaultProps);
          
          // Apply default styles
          if (data.defaultStyles) {
            setTimeout(() => {
              designer.updateElement(elementId, {
                styles: { ...data.defaultStyles, left: x, top: y }
              });
            }, 0);
          }
        }
      }
    } catch (error) {
      console.error('Failed to handle drop:', error);
    }
  }, [designer]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  // Mouse event listeners
  useEffect(() => {
    if (dragState.isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragState.isDragging, handleMouseMove, handleMouseUp]);

  // Render element
  const renderElement = useCallback((element: UIElement) => {
    const isSelected = designer.selectedElement?.id === element.id;
    const isHovered = designer.hoveredElement?.id === element.id;

    const elementStyle = {
      ...element.styles,
      position: 'absolute' as const,
      left: element.position.x,
      top: element.position.y,
      width: element.position.width,
      height: element.position.height,
      cursor: 'move',
      userSelect: 'none' as const
    };

    return (
      <div key={element.id} className="relative">
        {/* Element */}
        <div
          style={elementStyle}
          onMouseDown={(e) => handleElementMouseDown(e, element.id)}
          onMouseEnter={() => designer.setHoveredElement(element.id)}
          onMouseLeave={() => designer.setHoveredElement(null)}
          className={`
            ${isSelected ? 'ring-2 ring-primary ring-offset-2' : ''}
            ${isHovered && !isSelected ? 'ring-1 ring-primary/50' : ''}
            ${showElementBounds ? 'border border-dashed border-gray-300' : ''}
          `}
        >
          {/* Element content based on type */}
          {element.type === 'div' && (
            <div className="w-full h-full flex items-center justify-center text-xs text-gray-500">
              {element.name || 'Container'}
            </div>
          )}
          {element.type === 'h1' && (
            <h1 style={{ margin: 0, fontSize: 'inherit', fontWeight: 'inherit' }}>
              {element.props?.children || 'Heading 1'}
            </h1>
          )}
          {element.type === 'h2' && (
            <h2 style={{ margin: 0, fontSize: 'inherit', fontWeight: 'inherit' }}>
              {element.props?.children || 'Heading 2'}
            </h2>
          )}
          {element.type === 'h3' && (
            <h3 style={{ margin: 0, fontSize: 'inherit', fontWeight: 'inherit' }}>
              {element.props?.children || 'Heading 3'}
            </h3>
          )}
          {element.type === 'p' && (
            <p style={{ margin: 0, fontSize: 'inherit', lineHeight: 'inherit' }}>
              {element.props?.children || 'Paragraph text'}
            </p>
          )}
          {element.type === 'span' && (
            <span style={{ fontSize: 'inherit' }}>
              {element.props?.children || 'Inline text'}
            </span>
          )}
          {element.type === 'button' && (
            <button 
              style={{ 
                margin: 0, 
                fontSize: 'inherit', 
                fontWeight: 'inherit',
                padding: 'inherit',
                backgroundColor: 'inherit',
                color: 'inherit',
                border: 'inherit',
                borderRadius: 'inherit',
                cursor: 'inherit'
              }}
              onClick={(e) => e.preventDefault()}
            >
              {element.props?.children || 'Button'}
            </button>
          )}
          {element.type === 'input' && (
            <input 
              {...element.props}
              style={{ 
                margin: 0, 
                fontSize: 'inherit',
                padding: 'inherit',
                border: 'inherit',
                borderRadius: 'inherit',
                width: '100%',
                height: '100%'
              }}
              readOnly
            />
          )}
          {element.type === 'img' && (
            <img 
              {...element.props}
              style={{ 
                width: '100%',
                height: '100%',
                objectFit: 'inherit',
                borderRadius: 'inherit'
              }}
              alt={element.props?.alt || 'Image'}
            />
          )}
          
          {/* Render children */}
          {element.children.map(child => renderElement(child))}
        </div>

        {/* Selection handles */}
        {isSelected && (
          <div className="absolute inset-0 pointer-events-none">
            {/* Corner handles */}
            <div className="absolute -top-1 -left-1 w-2 h-2 bg-primary border border-white rounded-sm pointer-events-auto cursor-nw-resize" />
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary border border-white rounded-sm pointer-events-auto cursor-ne-resize" />
            <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-primary border border-white rounded-sm pointer-events-auto cursor-sw-resize" />
            <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-primary border border-white rounded-sm pointer-events-auto cursor-se-resize" />
            
            {/* Edge handles */}
            <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-primary border border-white rounded-sm pointer-events-auto cursor-n-resize" />
            <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-primary border border-white rounded-sm pointer-events-auto cursor-s-resize" />
            <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-primary border border-white rounded-sm pointer-events-auto cursor-w-resize" />
            <div className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-primary border border-white rounded-sm pointer-events-auto cursor-e-resize" />
          </div>
        )}

        {/* Element label */}
        {(isSelected || isHovered) && (
          <div className="absolute -top-6 left-0 bg-primary text-primary-foreground text-xs px-2 py-1 rounded whitespace-nowrap pointer-events-none">
            {element.name || element.type}
          </div>
        )}
      </div>
    );
  }, [designer, showElementBounds, handleElementMouseDown]);

  return (
    <div className="flex-1 relative overflow-hidden bg-gray-50">
      {/* Canvas Toolbar */}
      <div className="absolute top-4 left-4 z-10 flex items-center space-x-2 bg-background border border-border rounded-md shadow-sm p-2">
        <button
          onClick={() => setShowElementBounds(!showElementBounds)}
          className={`p-1 rounded transition-colors ${
            showElementBounds 
              ? 'bg-primary text-primary-foreground' 
              : 'hover:bg-accent'
          }`}
          title="Toggle Element Bounds"
        >
          {showElementBounds ? <Eye size={14} /> : <EyeOff size={14} />}
        </button>
        
        <div className="w-px h-4 bg-border" />
        
        <span className="text-xs text-muted-foreground">
          {designer.state.elements.length} elements
        </span>
      </div>

      {/* Canvas */}
      <div
        ref={canvasRef}
        className="w-full h-full relative cursor-grab active:cursor-grabbing"
        onMouseDown={handleCanvasMouseDown}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        style={{
          transform: `scale(${designer.state.canvas.zoom}) translate(${designer.state.canvas.pan.x}px, ${designer.state.canvas.pan.y}px)`,
          transformOrigin: '0 0'
        }}
      >
        {/* Grid */}
        {designer.state.canvas.grid && (
          <div 
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `
                linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />
        )}

        {/* Device Frame */}
        <div 
          className="relative bg-white border border-gray-300 shadow-lg"
          style={{
            width: designer.state.canvas.device.width,
            height: designer.state.canvas.device.height,
            margin: '50px'
          }}
        >
          {/* Elements */}
          {designer.state.elements.map(element => renderElement(element))}
          
          {/* Drop zone indicator */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="w-full h-full border-2 border-dashed border-transparent hover:border-primary/30 transition-colors" />
          </div>
        </div>
      </div>

      {/* Canvas Info */}
      <div className="absolute bottom-4 left-4 bg-background border border-border rounded-md shadow-sm p-2 text-xs text-muted-foreground">
        <div>Zoom: {Math.round(designer.state.canvas.zoom * 100)}%</div>
        <div>Device: {designer.state.canvas.device.name}</div>
        <div>Size: {designer.state.canvas.device.width} × {designer.state.canvas.device.height}</div>
      </div>
    </div>
  );
}
