import { motion } from 'framer-motion';
import {
    ChevronDown,
    ChevronRight,
    Grid3X3,
    Image,
    Layers,
    Layout,
    MousePointer,
    Search,
    Square,
    Type,
    Zap
} from 'lucide-react';
import React, { useState } from 'react';
import { ElementType, UseUIDesignerResult } from '../types';

interface ElementPaletteProps {
  designer: UseUIDesignerResult;
}

interface ElementCategory {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  elements: ElementDefinition[];
  collapsed?: boolean;
}

interface ElementDefinition {
  type: ElementType;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  defaultProps?: any;
  defaultStyles?: any;
}

const ELEMENT_CATEGORIES: ElementCategory[] = [
  {
    id: 'layout',
    name: 'Layout',
    icon: Layout,
    elements: [
      {
        type: 'div',
        name: 'Container',
        description: 'Basic container element',
        icon: Square,
        defaultStyles: {
          display: 'block',
          padding: 16,
          backgroundColor: '#f3f4f6',
          border: '1px solid #e5e7eb',
          borderRadius: 8
        }
      },
      {
        type: 'Flex',
        name: 'Flex Container',
        description: 'Flexbox layout container',
        icon: Grid3X3,
        defaultStyles: {
          display: 'flex',
          flexDirection: 'row',
          gap: 8,
          padding: 16,
          backgroundColor: '#f9fafb',
          border: '1px dashed #d1d5db',
          borderRadius: 8
        }
      },
      {
        type: 'Grid',
        name: 'Grid Container',
        description: 'CSS Grid layout container',
        icon: Grid3X3,
        defaultStyles: {
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: 16,
          padding: 16,
          backgroundColor: '#f9fafb',
          border: '1px dashed #d1d5db',
          borderRadius: 8
        }
      },
      {
        type: 'section',
        name: 'Section',
        description: 'Semantic section element',
        icon: Layers,
        defaultStyles: {
          padding: 24,
          backgroundColor: '#ffffff',
          border: '1px solid #e5e7eb',
          borderRadius: 8
        }
      }
    ]
  },
  {
    id: 'typography',
    name: 'Typography',
    icon: Type,
    elements: [
      {
        type: 'h1',
        name: 'Heading 1',
        description: 'Main page heading',
        icon: Type,
        defaultProps: { children: 'Heading 1' },
        defaultStyles: {
          fontSize: 32,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 16
        }
      },
      {
        type: 'h2',
        name: 'Heading 2',
        description: 'Section heading',
        icon: Type,
        defaultProps: { children: 'Heading 2' },
        defaultStyles: {
          fontSize: 24,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 12
        }
      },
      {
        type: 'h3',
        name: 'Heading 3',
        description: 'Subsection heading',
        icon: Type,
        defaultProps: { children: 'Heading 3' },
        defaultStyles: {
          fontSize: 20,
          fontWeight: 'semibold',
          color: '#111827',
          marginBottom: 8
        }
      },
      {
        type: 'p',
        name: 'Paragraph',
        description: 'Text paragraph',
        icon: Type,
        defaultProps: { children: 'This is a paragraph of text.' },
        defaultStyles: {
          fontSize: 16,
          lineHeight: 1.6,
          color: '#374151',
          marginBottom: 16
        }
      },
      {
        type: 'span',
        name: 'Inline Text',
        description: 'Inline text element',
        icon: Type,
        defaultProps: { children: 'Inline text' },
        defaultStyles: {
          fontSize: 16,
          color: '#374151'
        }
      }
    ]
  },
  {
    id: 'form',
    name: 'Form Elements',
    icon: MousePointer,
    elements: [
      {
        type: 'button',
        name: 'Button',
        description: 'Interactive button',
        icon: MousePointer,
        defaultProps: { children: 'Click me' },
        defaultStyles: {
          padding: '8px 16px',
          backgroundColor: '#3b82f6',
          color: '#ffffff',
          border: 'none',
          borderRadius: 6,
          fontSize: 14,
          fontWeight: 'medium',
          cursor: 'pointer'
        }
      },
      {
        type: 'input',
        name: 'Text Input',
        description: 'Text input field',
        icon: MousePointer,
        defaultProps: {
          type: 'text',
          placeholder: 'Enter text...'
        },
        defaultStyles: {
          padding: '8px 12px',
          border: '1px solid #d1d5db',
          borderRadius: 6,
          fontSize: 14,
          width: 200
        }
      },
      {
        type: 'textarea',
        name: 'Text Area',
        description: 'Multi-line text input',
        icon: MousePointer,
        defaultProps: {
          placeholder: 'Enter text...',
          rows: 4
        },
        defaultStyles: {
          padding: '8px 12px',
          border: '1px solid #d1d5db',
          borderRadius: 6,
          fontSize: 14,
          width: 200,
          resize: 'vertical'
        }
      },
      {
        type: 'select',
        name: 'Select',
        description: 'Dropdown select',
        icon: MousePointer,
        defaultProps: {
          children: [
            { type: 'option', props: { value: '1', children: 'Option 1' } },
            { type: 'option', props: { value: '2', children: 'Option 2' } }
          ]
        },
        defaultStyles: {
          padding: '8px 12px',
          border: '1px solid #d1d5db',
          borderRadius: 6,
          fontSize: 14,
          width: 200
        }
      }
    ]
  },
  {
    id: 'media',
    name: 'Media',
    icon: Image,
    elements: [
      {
        type: 'img',
        name: 'Image',
        description: 'Image element',
        icon: Image,
        defaultProps: {
          src: 'https://via.placeholder.com/200x150',
          alt: 'Placeholder image'
        },
        defaultStyles: {
          width: 200,
          height: 150,
          objectFit: 'cover',
          borderRadius: 8
        }
      },
      {
        type: 'video',
        name: 'Video',
        description: 'Video element',
        icon: Image,
        defaultProps: {
          controls: true,
          width: 300,
          height: 200
        },
        defaultStyles: {
          borderRadius: 8
        }
      }
    ]
  },
  {
    id: 'components',
    name: 'Components',
    icon: Zap,
    elements: [
      {
        type: 'Card',
        name: 'Card',
        description: 'Card component',
        icon: Square,
        defaultStyles: {
          padding: 24,
          backgroundColor: '#ffffff',
          border: '1px solid #e5e7eb',
          borderRadius: 12,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }
      },
      {
        type: 'Button',
        name: 'UI Button',
        description: 'Styled button component',
        icon: MousePointer,
        defaultProps: {
          variant: 'primary',
          children: 'Button'
        },
        defaultStyles: {
          padding: '12px 24px',
          backgroundColor: '#3b82f6',
          color: '#ffffff',
          border: 'none',
          borderRadius: 8,
          fontSize: 16,
          fontWeight: 'medium',
          cursor: 'pointer'
        }
      }
    ]
  }
];

export function ElementPalette({ designer }: ElementPaletteProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [collapsedCategories, setCollapsedCategories] = useState<Set<string>>(new Set());

  // Filter elements based on search
  const filteredCategories = ELEMENT_CATEGORIES.map(category => ({
    ...category,
    elements: category.elements.filter(element =>
      element.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      element.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.elements.length > 0);

  // Toggle category collapse
  const toggleCategory = (categoryId: string) => {
    const newCollapsed = new Set(collapsedCategories);
    if (newCollapsed.has(categoryId)) {
      newCollapsed.delete(categoryId);
    } else {
      newCollapsed.add(categoryId);
    }
    setCollapsedCategories(newCollapsed);
  };

  // Handle element drag start
  const handleDragStart = (e: React.DragEvent, element: ElementDefinition) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'element',
      elementType: element.type,
      defaultProps: element.defaultProps || {},
      defaultStyles: element.defaultStyles || {}
    }));
    e.dataTransfer.effectAllowed = 'copy';
  };

  // Handle element click (add to canvas)
  const handleElementClick = (element: ElementDefinition) => {
    const position = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    };

    designer.addElement(element.type, position, element.defaultProps);

    // Apply default styles if provided
    if (element.defaultStyles) {
      setTimeout(() => {
        const addedElement = designer.state.elements[designer.state.elements.length - 1];
        if (addedElement) {
          designer.updateElement(addedElement.id, {
            styles: { ...addedElement.styles, ...element.defaultStyles }
          });
        }
      }, 0);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Search */}
      <div className="p-3 border-b border-border">
        <div className="relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search elements..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-9 pr-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>
      </div>

      {/* Element Categories */}
      <div className="flex-1 overflow-y-auto">
        {filteredCategories.map(category => {
          const isCollapsed = collapsedCategories.has(category.id);
          const CategoryIcon = category.icon;

          return (
            <div key={category.id} className="border-b border-border last:border-b-0">
              {/* Category Header */}
              <button
                onClick={() => toggleCategory(category.id)}
                className="w-full flex items-center justify-between p-3 hover:bg-accent transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <CategoryIcon size={16} className="text-muted-foreground" />
                  <span className="text-sm font-medium">{category.name}</span>
                  <span className="text-xs text-muted-foreground">
                    ({category.elements.length})
                  </span>
                </div>
                {isCollapsed ? (
                  <ChevronRight size={16} className="text-muted-foreground" />
                ) : (
                  <ChevronDown size={16} className="text-muted-foreground" />
                )}
              </button>

              {/* Category Elements */}
              {!isCollapsed && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-2 space-y-1">
                    {category.elements.map(element => {
                      const ElementIcon = element.icon;

                      return (
                        <div
                          key={element.type}
                          draggable
                          onDragStart={(e) => handleDragStart(e, element)}
                          onClick={() => handleElementClick(element)}
                          className="flex items-center space-x-3 p-2 rounded-md hover:bg-accent cursor-pointer transition-colors group"
                          title={element.description}
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-secondary rounded-md flex items-center justify-center group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                            <ElementIcon size={14} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium truncate">
                              {element.name}
                            </div>
                            <div className="text-xs text-muted-foreground truncate">
                              {element.description}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </motion.div>
              )}
            </div>
          );
        })}

        {filteredCategories.length === 0 && (
          <div className="p-8 text-center text-muted-foreground">
            <Search size={32} className="mx-auto mb-4 opacity-50" />
            <p className="text-sm">No elements found</p>
            <p className="text-xs mt-1">Try a different search term</p>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="p-3 border-t border-border bg-secondary/50">
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Click to add element</p>
          <p>• Drag to canvas to position</p>
          <p>• Use search to find elements</p>
        </div>
      </div>
    </div>
  );
}
