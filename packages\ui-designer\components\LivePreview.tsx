import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { 
  RefreshCw, 
  ExternalLink, 
  Smartphone, 
  Tablet, 
  Monitor, 
  MonitorSpeaker,
  Eye,
  Code
} from 'lucide-react';
import { UIElement } from '../types';

interface LivePreviewProps {
  elements: UIElement[];
  className?: string;
}

interface DevicePreset {
  name: string;
  width: number;
  height: number;
  icon: React.ComponentType<any>;
}

const DEVICE_PRESETS: DevicePreset[] = [
  { name: 'Mobile', width: 375, height: 667, icon: Smartphone },
  { name: 'Tablet', width: 768, height: 1024, icon: Tablet },
  { name: 'Desktop', width: 1440, height: 900, icon: Monitor },
  { name: 'Wide', width: 1920, height: 1080, icon: MonitorSpeaker }
];

export function LivePreview({ elements, className = '' }: LivePreviewProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [selectedDevice, setSelectedDevice] = useState(DEVICE_PRESETS[2]); // Desktop
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [previewMode, setPreviewMode] = useState<'responsive' | 'device'>('responsive');

  // Generate HTML from elements
  const generateHTML = (elements: UIElement[]): string => {
    const renderElement = (element: UIElement): string => {
      const styles = Object.entries(element.styles || {})
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}${typeof value === 'number' && !['opacity', 'zIndex', 'fontWeight'].includes(key) ? 'px' : ''}`)
        .join('; ');

      const props = Object.entries(element.props || {})
        .filter(([key]) => key !== 'children')
        .map(([key, value]) => `${key}="${value}"`)
        .join(' ');

      const children = element.children.map(renderElement).join('');
      const textContent = element.props?.children || '';

      // Handle different element types
      switch (element.type) {
        case 'img':
          return `<img ${props} style="${styles}" />`;
        case 'input':
          return `<input ${props} style="${styles}" />`;
        case 'textarea':
          return `<textarea ${props} style="${styles}">${textContent}</textarea>`;
        case 'select':
          const options = Array.isArray(element.props?.children) 
            ? element.props.children.map((opt: any) => `<option value="${opt.props?.value || ''}">${opt.props?.children || ''}</option>`).join('')
            : '';
          return `<select ${props} style="${styles}">${options}</select>`;
        case 'button':
          return `<button ${props} style="${styles}">${textContent}${children}</button>`;
        case 'Flex':
          return `<div ${props} style="display: flex; ${styles}">${textContent}${children}</div>`;
        case 'Grid':
          return `<div ${props} style="display: grid; ${styles}">${textContent}${children}</div>`;
        case 'Card':
          return `<div ${props} style="${styles}" class="card">${textContent}${children}</div>`;
        case 'Button':
          return `<button ${props} style="${styles}" class="ui-button">${textContent}${children}</button>`;
        default:
          const tag = element.type;
          if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div', 'section', 'article', 'header', 'footer', 'nav', 'main', 'aside'].includes(tag)) {
            return `<${tag} ${props} style="${styles}">${textContent}${children}</${tag}>`;
          }
          return `<div ${props} style="${styles}">${textContent}${children}</div>`;
      }
    };

    const elementsHTML = elements.map(renderElement).join('');

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KodeKilat Studio Preview</title>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            min-height: 100vh;
        }
        .card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .ui-button {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .ui-button:hover {
            background: #2563eb;
        }
        input, textarea, select {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body>
    ${elementsHTML}
</body>
</html>`;
  };

  // Update iframe content
  const updatePreview = () => {
    if (!iframeRef.current) return;

    setIsRefreshing(true);
    const html = generateHTML(elements);
    
    const iframe = iframeRef.current;
    iframe.srcdoc = html;
    
    setTimeout(() => setIsRefreshing(false), 500);
  };

  // Update preview when elements change
  useEffect(() => {
    updatePreview();
  }, [elements]);

  // Handle device change
  const handleDeviceChange = (device: DevicePreset) => {
    setSelectedDevice(device);
    setPreviewMode('device');
  };

  // Handle refresh
  const handleRefresh = () => {
    updatePreview();
  };

  // Handle open in new tab
  const handleOpenInNewTab = () => {
    const html = generateHTML(elements);
    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    window.open(url, '_blank');
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  };

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-border bg-secondary/50">
        <div className="flex items-center space-x-2">
          <Eye size={16} className="text-muted-foreground" />
          <span className="text-sm font-medium">Live Preview</span>
          <span className="text-xs text-muted-foreground">
            ({elements.length} elements)
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* Device Presets */}
          <div className="flex items-center space-x-1 bg-background rounded-md p-1">
            <button
              onClick={() => setPreviewMode('responsive')}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                previewMode === 'responsive' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'hover:bg-accent'
              }`}
            >
              Responsive
            </button>
            {DEVICE_PRESETS.map(device => {
              const DeviceIcon = device.icon;
              return (
                <button
                  key={device.name}
                  onClick={() => handleDeviceChange(device)}
                  className={`p-1 rounded transition-colors ${
                    previewMode === 'device' && selectedDevice.name === device.name
                      ? 'bg-primary text-primary-foreground' 
                      : 'hover:bg-accent'
                  }`}
                  title={`${device.name} (${device.width}×${device.height})`}
                >
                  <DeviceIcon size={14} />
                </button>
              );
            })}
          </div>

          <div className="w-px h-6 bg-border" />

          {/* Actions */}
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="p-2 hover:bg-accent rounded disabled:opacity-50"
            title="Refresh Preview"
          >
            <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
          </button>
          
          <button
            onClick={handleOpenInNewTab}
            className="p-2 hover:bg-accent rounded"
            title="Open in New Tab"
          >
            <ExternalLink size={16} />
          </button>
        </div>
      </div>

      {/* Preview Area */}
      <div className="flex-1 flex items-center justify-center p-4 bg-gray-100">
        {previewMode === 'responsive' ? (
          <div className="w-full h-full bg-white border border-border rounded-lg shadow-sm overflow-hidden">
            <iframe
              ref={iframeRef}
              className="w-full h-full border-0"
              title="Live Preview"
              sandbox="allow-scripts allow-same-origin"
            />
          </div>
        ) : (
          <motion.div
            key={selectedDevice.name}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.2 }}
            className="bg-white border border-border rounded-lg shadow-lg overflow-hidden"
            style={{
              width: selectedDevice.width,
              height: selectedDevice.height,
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            <iframe
              ref={iframeRef}
              className="w-full h-full border-0"
              title="Live Preview"
              sandbox="allow-scripts allow-same-origin"
            />
          </motion.div>
        )}
      </div>

      {/* Device Info */}
      {previewMode === 'device' && (
        <div className="p-3 border-t border-border bg-secondary/50 text-center">
          <div className="text-xs text-muted-foreground">
            {selectedDevice.name} • {selectedDevice.width} × {selectedDevice.height}px
          </div>
        </div>
      )}
    </div>
  );
}
