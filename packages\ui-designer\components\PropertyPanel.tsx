import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Palette, 
  Layout, 
  Type, 
  Spacing,
  ChevronDown,
  ChevronRight,
  Trash2,
  <PERSON><PERSON>,
  <PERSON>,
  EyeOff
} from 'lucide-react';
import { UIElement, UseUIDesignerResult, StyleProperties } from '../types';

interface PropertyPanelProps {
  designer: UseUIDesignerResult;
  selectedElement: UIElement | null;
}

interface PropertySection {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  collapsed?: boolean;
}

const PROPERTY_SECTIONS: PropertySection[] = [
  { id: 'general', name: 'General', icon: Settings },
  { id: 'layout', name: 'Layout', icon: Layout },
  { id: 'typography', name: 'Typography', icon: Type },
  { id: 'appearance', name: 'Appearance', icon: Palette },
  { id: 'spacing', name: 'Spacing', icon: Spacing }
];

export function PropertyPanel({ designer, selectedElement }: PropertyPanelProps) {
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(new Set());

  // Toggle section collapse
  const toggleSection = (sectionId: string) => {
    const newCollapsed = new Set(collapsedSections);
    if (newCollapsed.has(sectionId)) {
      newCollapsed.delete(sectionId);
    } else {
      newCollapsed.add(sectionId);
    }
    setCollapsedSections(newCollapsed);
  };

  // Update element property
  const updateProperty = (property: string, value: any) => {
    if (!selectedElement) return;
    
    if (property.startsWith('props.')) {
      const propName = property.replace('props.', '');
      designer.updateElement(selectedElement.id, {
        props: { ...selectedElement.props, [propName]: value }
      });
    } else if (property.startsWith('styles.')) {
      const styleName = property.replace('styles.', '');
      designer.updateElement(selectedElement.id, {
        styles: { ...selectedElement.styles, [styleName]: value }
      });
    } else if (property === 'name') {
      designer.updateElement(selectedElement.id, { name: value });
    }
  };

  // Get property value
  const getPropertyValue = (property: string): any => {
    if (!selectedElement) return '';
    
    if (property.startsWith('props.')) {
      const propName = property.replace('props.', '');
      return selectedElement.props?.[propName] || '';
    } else if (property.startsWith('styles.')) {
      const styleName = property.replace('styles.', '');
      return selectedElement.styles?.[styleName] || '';
    } else if (property === 'name') {
      return selectedElement.name || '';
    }
    return '';
  };

  // Render input field
  const renderInput = (
    label: string, 
    property: string, 
    type: 'text' | 'number' | 'color' | 'select' = 'text',
    options?: string[]
  ) => {
    const value = getPropertyValue(property);
    
    return (
      <div className="space-y-1">
        <label className="text-xs font-medium text-muted-foreground">
          {label}
        </label>
        {type === 'select' ? (
          <select
            value={value}
            onChange={(e) => updateProperty(property, e.target.value)}
            className="w-full px-2 py-1 text-sm bg-background border border-border rounded focus:outline-none focus:ring-1 focus:ring-primary"
          >
            {options?.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        ) : (
          <input
            type={type}
            value={value}
            onChange={(e) => updateProperty(property, type === 'number' ? Number(e.target.value) : e.target.value)}
            className="w-full px-2 py-1 text-sm bg-background border border-border rounded focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>
    );
  };

  // Render section
  const renderSection = (section: PropertySection) => {
    const isCollapsed = collapsedSections.has(section.id);
    const SectionIcon = section.icon;

    return (
      <div key={section.id} className="border-b border-border last:border-b-0">
        {/* Section Header */}
        <button
          onClick={() => toggleSection(section.id)}
          className="w-full flex items-center justify-between p-3 hover:bg-accent transition-colors"
        >
          <div className="flex items-center space-x-2">
            <SectionIcon size={16} className="text-muted-foreground" />
            <span className="text-sm font-medium">{section.name}</span>
          </div>
          {isCollapsed ? (
            <ChevronRight size={16} className="text-muted-foreground" />
          ) : (
            <ChevronDown size={16} className="text-muted-foreground" />
          )}
        </button>

        {/* Section Content */}
        {!isCollapsed && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-3 space-y-3">
              {section.id === 'general' && (
                <>
                  {renderInput('Name', 'name')}
                  {renderInput('ID', 'props.id')}
                  {renderInput('Class', 'props.className')}
                  {selectedElement?.type === 'img' && (
                    <>
                      {renderInput('Source', 'props.src')}
                      {renderInput('Alt Text', 'props.alt')}
                    </>
                  )}
                  {(selectedElement?.type === 'input' || selectedElement?.type === 'textarea') && (
                    <>
                      {renderInput('Placeholder', 'props.placeholder')}
                      {selectedElement.type === 'input' && renderInput('Type', 'props.type', 'select', ['text', 'email', 'password', 'number', 'tel', 'url'])}
                    </>
                  )}
                  {(selectedElement?.type.includes('h') || selectedElement?.type === 'p' || selectedElement?.type === 'span' || selectedElement?.type === 'button') && (
                    renderInput('Text Content', 'props.children')
                  )}
                </>
              )}

              {section.id === 'layout' && (
                <>
                  <div className="grid grid-cols-2 gap-2">
                    {renderInput('Width', 'styles.width', 'number')}
                    {renderInput('Height', 'styles.height', 'number')}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {renderInput('X Position', 'styles.left', 'number')}
                    {renderInput('Y Position', 'styles.top', 'number')}
                  </div>
                  {renderInput('Display', 'styles.display', 'select', ['block', 'inline', 'inline-block', 'flex', 'grid', 'none'])}
                  {getPropertyValue('styles.display') === 'flex' && (
                    <>
                      {renderInput('Flex Direction', 'styles.flexDirection', 'select', ['row', 'column', 'row-reverse', 'column-reverse'])}
                      {renderInput('Justify Content', 'styles.justifyContent', 'select', ['flex-start', 'center', 'flex-end', 'space-between', 'space-around', 'space-evenly'])}
                      {renderInput('Align Items', 'styles.alignItems', 'select', ['flex-start', 'center', 'flex-end', 'stretch', 'baseline'])}
                      {renderInput('Gap', 'styles.gap', 'number')}
                    </>
                  )}
                  {getPropertyValue('styles.display') === 'grid' && (
                    <>
                      {renderInput('Grid Template Columns', 'styles.gridTemplateColumns')}
                      {renderInput('Grid Template Rows', 'styles.gridTemplateRows')}
                      {renderInput('Gap', 'styles.gap', 'number')}
                    </>
                  )}
                </>
              )}

              {section.id === 'typography' && (
                <>
                  {renderInput('Font Size', 'styles.fontSize', 'number')}
                  {renderInput('Font Weight', 'styles.fontWeight', 'select', ['normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900'])}
                  {renderInput('Font Family', 'styles.fontFamily')}
                  {renderInput('Line Height', 'styles.lineHeight', 'number')}
                  {renderInput('Text Align', 'styles.textAlign', 'select', ['left', 'center', 'right', 'justify'])}
                  {renderInput('Color', 'styles.color', 'color')}
                  {renderInput('Text Decoration', 'styles.textDecoration', 'select', ['none', 'underline', 'line-through', 'overline'])}
                </>
              )}

              {section.id === 'appearance' && (
                <>
                  {renderInput('Background Color', 'styles.backgroundColor', 'color')}
                  {renderInput('Border', 'styles.border')}
                  {renderInput('Border Radius', 'styles.borderRadius', 'number')}
                  {renderInput('Box Shadow', 'styles.boxShadow')}
                  {renderInput('Opacity', 'styles.opacity', 'number')}
                  {renderInput('Overflow', 'styles.overflow', 'select', ['visible', 'hidden', 'scroll', 'auto'])}
                </>
              )}

              {section.id === 'spacing' && (
                <>
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground">Margin</label>
                    <div className="grid grid-cols-2 gap-1">
                      {renderInput('Top', 'styles.marginTop', 'number')}
                      {renderInput('Right', 'styles.marginRight', 'number')}
                      {renderInput('Bottom', 'styles.marginBottom', 'number')}
                      {renderInput('Left', 'styles.marginLeft', 'number')}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-muted-foreground">Padding</label>
                    <div className="grid grid-cols-2 gap-1">
                      {renderInput('Top', 'styles.paddingTop', 'number')}
                      {renderInput('Right', 'styles.paddingRight', 'number')}
                      {renderInput('Bottom', 'styles.paddingBottom', 'number')}
                      {renderInput('Left', 'styles.paddingLeft', 'number')}
                    </div>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        )}
      </div>
    );
  };

  if (!selectedElement) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-3 border-b border-border">
          <h3 className="text-sm font-medium">Properties</h3>
        </div>
        <div className="flex-1 flex items-center justify-center text-center p-8">
          <div className="text-muted-foreground">
            <Settings size={32} className="mx-auto mb-4 opacity-50" />
            <p className="text-sm">Select an element to edit its properties</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-3 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Properties</h3>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => designer.copyElement(selectedElement.id)}
              className="p-1 hover:bg-accent rounded"
              title="Copy Element"
            >
              <Copy size={14} />
            </button>
            <button
              onClick={() => designer.removeElement(selectedElement.id)}
              className="p-1 hover:bg-accent rounded text-destructive"
              title="Delete Element"
            >
              <Trash2 size={14} />
            </button>
          </div>
        </div>
        <div className="mt-2 text-xs text-muted-foreground">
          {selectedElement.type} • {selectedElement.name || selectedElement.id}
        </div>
      </div>

      {/* Properties */}
      <div className="flex-1 overflow-y-auto">
        {PROPERTY_SECTIONS.map(section => renderSection(section))}
      </div>
    </div>
  );
}
