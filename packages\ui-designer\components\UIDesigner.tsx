import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Palette, 
  Settings, 
  Eye, 
  Code, 
  Layers, 
  Grid, 
  Ruler, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw,
  Play,
  Download,
  Upload,
  Save,
  Undo,
  Redo,
  Copy,
  Scissors,
  Clipboard
} from 'lucide-react';
import { useUIDesigner } from '../hooks/useUIDesigner';
import { ElementPalette } from './ElementPalette';
import { DesignCanvas } from './DesignCanvas';
import { PropertyPanel } from './PropertyPanel';
import { LivePreview } from './LivePreview';
import { CodeGenerator } from './CodeGenerator';
import { UIElement } from '../types';

interface UIDesignerProps {
  className?: string;
  onSave?: (design: UIElement[]) => void;
  onLoad?: () => UIElement[] | Promise<UIElement[]>;
  initialDesign?: UIElement[];
}

type ViewMode = 'design' | 'preview' | 'code';
type PanelMode = 'elements' | 'properties' | 'layers';

export function UIDesigner({ 
  className = '', 
  onSave, 
  onLoad, 
  initialDesign 
}: UIDesignerProps) {
  const designer = useUIDesigner();
  const [viewMode, setViewMode] = useState<ViewMode>('design');
  const [leftPanel, setLeftPanel] = useState<PanelMode>('elements');
  const [rightPanelOpen, setRightPanelOpen] = useState(true);
  const [leftPanelOpen, setLeftPanelOpen] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load initial design
  React.useEffect(() => {
    if (initialDesign) {
      designer.importDesign(initialDesign);
    }
  }, [initialDesign, designer]);

  // Handle save
  const handleSave = useCallback(async () => {
    const design = designer.exportDesign();
    if (onSave) {
      onSave(design);
    } else {
      // Default save to localStorage
      localStorage.setItem('kodekilat-ui-design', JSON.stringify(design));
    }
  }, [designer, onSave]);

  // Handle load
  const handleLoad = useCallback(async () => {
    if (onLoad) {
      const design = await onLoad();
      designer.importDesign(design);
    } else {
      // Default load from localStorage
      const saved = localStorage.getItem('kodekilat-ui-design');
      if (saved) {
        try {
          const design = JSON.parse(saved);
          designer.importDesign(design);
        } catch (error) {
          console.error('Failed to load design:', error);
        }
      }
    }
  }, [designer, onLoad]);

  // Handle file import
  const handleFileImport = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const design = JSON.parse(e.target?.result as string);
          designer.importDesign(design);
        } catch (error) {
          console.error('Failed to import design:', error);
        }
      };
      reader.readAsText(file);
    }
  }, [designer]);

  // Handle file export
  const handleFileExport = useCallback(() => {
    const design = designer.exportDesign();
    const blob = new Blob([JSON.stringify(design, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'kodekilat-design.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [designer]);

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              designer.redo();
            } else {
              designer.undo();
            }
            break;
          case 's':
            e.preventDefault();
            handleSave();
            break;
          case 'o':
            e.preventDefault();
            handleLoad();
            break;
          case 'c':
            e.preventDefault();
            if (designer.selectedElement) {
              designer.copyElement(designer.selectedElement.id);
            }
            break;
          case 'v':
            e.preventDefault();
            designer.pasteElement();
            break;
          case 'x':
            e.preventDefault();
            if (designer.selectedElement) {
              designer.cutElement(designer.selectedElement.id);
            }
            break;
          case 'd':
            e.preventDefault();
            if (designer.selectedElement) {
              designer.duplicateElement(designer.selectedElement.id);
            }
            break;
        }
      }

      // Delete key
      if (e.key === 'Delete' && designer.selectedElement) {
        designer.removeElement(designer.selectedElement.id);
      }

      // Escape key
      if (e.key === 'Escape') {
        designer.selectElement(null);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [designer, handleSave, handleLoad]);

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-secondary/50">
        {/* Left Toolbar */}
        <div className="flex items-center space-x-1">
          {/* View Mode Tabs */}
          <div className="flex bg-background rounded-md p-1 mr-4">
            <button
              onClick={() => setViewMode('design')}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                viewMode === 'design' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'hover:bg-accent'
              }`}
            >
              Design
            </button>
            <button
              onClick={() => setViewMode('preview')}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                viewMode === 'preview' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'hover:bg-accent'
              }`}
            >
              Preview
            </button>
            <button
              onClick={() => setViewMode('code')}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                viewMode === 'code' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'hover:bg-accent'
              }`}
            >
              Code
            </button>
          </div>

          {/* Action Buttons */}
          <button
            onClick={designer.undo}
            disabled={!designer.canUndo}
            className="p-2 hover:bg-accent rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Undo (Ctrl+Z)"
          >
            <Undo size={16} />
          </button>
          <button
            onClick={designer.redo}
            disabled={!designer.canRedo}
            className="p-2 hover:bg-accent rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Redo (Ctrl+Shift+Z)"
          >
            <Redo size={16} />
          </button>

          <div className="w-px h-6 bg-border mx-2" />

          <button
            onClick={() => designer.selectedElement && designer.copyElement(designer.selectedElement.id)}
            disabled={!designer.selectedElement}
            className="p-2 hover:bg-accent rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Copy (Ctrl+C)"
          >
            <Copy size={16} />
          </button>
          <button
            onClick={() => designer.selectedElement && designer.cutElement(designer.selectedElement.id)}
            disabled={!designer.selectedElement}
            className="p-2 hover:bg-accent rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Cut (Ctrl+X)"
          >
            <Scissors size={16} />
          </button>
          <button
            onClick={() => designer.pasteElement()}
            disabled={!designer.state.clipboard}
            className="p-2 hover:bg-accent rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Paste (Ctrl+V)"
          >
            <Clipboard size={16} />
          </button>
        </div>

        {/* Center - Zoom Controls */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => designer.setZoom(designer.state.canvas.zoom - 0.1)}
            className="p-2 hover:bg-accent rounded"
            title="Zoom Out"
          >
            <ZoomOut size={16} />
          </button>
          <span className="text-sm font-mono min-w-[60px] text-center">
            {Math.round(designer.state.canvas.zoom * 100)}%
          </span>
          <button
            onClick={() => designer.setZoom(designer.state.canvas.zoom + 0.1)}
            className="p-2 hover:bg-accent rounded"
            title="Zoom In"
          >
            <ZoomIn size={16} />
          </button>
          <button
            onClick={() => designer.setZoom(1)}
            className="p-2 hover:bg-accent rounded"
            title="Reset Zoom"
          >
            <RotateCcw size={16} />
          </button>
        </div>

        {/* Right Toolbar */}
        <div className="flex items-center space-x-1">
          <button
            onClick={designer.toggleGrid}
            className={`p-2 rounded transition-colors ${
              designer.state.canvas.grid 
                ? 'bg-primary text-primary-foreground' 
                : 'hover:bg-accent'
            }`}
            title="Toggle Grid"
          >
            <Grid size={16} />
          </button>
          <button
            onClick={designer.toggleRulers}
            className={`p-2 rounded transition-colors ${
              designer.state.canvas.rulers 
                ? 'bg-primary text-primary-foreground' 
                : 'hover:bg-accent'
            }`}
            title="Toggle Rulers"
          >
            <Ruler size={16} />
          </button>

          <div className="w-px h-6 bg-border mx-2" />

          <button
            onClick={handleSave}
            className="p-2 hover:bg-accent rounded"
            title="Save (Ctrl+S)"
          >
            <Save size={16} />
          </button>
          <button
            onClick={handleLoad}
            className="p-2 hover:bg-accent rounded"
            title="Load (Ctrl+O)"
          >
            <Upload size={16} />
          </button>
          <button
            onClick={handleFileExport}
            className="p-2 hover:bg-accent rounded"
            title="Export Design"
          >
            <Download size={16} />
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleFileImport}
            className="hidden"
          />
          <button
            onClick={() => fileInputRef.current?.click()}
            className="p-2 hover:bg-accent rounded"
            title="Import Design"
          >
            <Upload size={16} />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Panel */}
        <AnimatePresence>
          {leftPanelOpen && (
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: 280 }}
              exit={{ width: 0 }}
              transition={{ duration: 0.2 }}
              className="border-r border-border bg-secondary/30 overflow-hidden"
            >
              <div className="h-full flex flex-col">
                {/* Panel Tabs */}
                <div className="flex border-b border-border">
                  <button
                    onClick={() => setLeftPanel('elements')}
                    className={`flex-1 px-3 py-2 text-sm transition-colors ${
                      leftPanel === 'elements' 
                        ? 'bg-background border-b-2 border-primary' 
                        : 'hover:bg-accent'
                    }`}
                  >
                    <Palette size={16} className="inline mr-2" />
                    Elements
                  </button>
                  <button
                    onClick={() => setLeftPanel('layers')}
                    className={`flex-1 px-3 py-2 text-sm transition-colors ${
                      leftPanel === 'layers' 
                        ? 'bg-background border-b-2 border-primary' 
                        : 'hover:bg-accent'
                    }`}
                  >
                    <Layers size={16} className="inline mr-2" />
                    Layers
                  </button>
                </div>

                {/* Panel Content */}
                <div className="flex-1 overflow-hidden">
                  {leftPanel === 'elements' && <ElementPalette designer={designer} />}
                  {leftPanel === 'layers' && (
                    <div className="p-4 text-center text-muted-foreground">
                      Layers panel coming soon...
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Center - Canvas/Preview/Code */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {viewMode === 'design' && <DesignCanvas designer={designer} />}
          {viewMode === 'preview' && <LivePreview elements={designer.state.elements} />}
          {viewMode === 'code' && <CodeGenerator elements={designer.state.elements} />}
        </div>

        {/* Right Panel - Properties */}
        <AnimatePresence>
          {rightPanelOpen && (
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: 320 }}
              exit={{ width: 0 }}
              transition={{ duration: 0.2 }}
              className="border-l border-border bg-secondary/30 overflow-hidden"
            >
              <PropertyPanel 
                designer={designer}
                selectedElement={designer.selectedElement}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Panel Toggle Buttons */}
      <button
        onClick={() => setLeftPanelOpen(!leftPanelOpen)}
        className="fixed left-2 top-1/2 transform -translate-y-1/2 p-2 bg-background border border-border rounded-md shadow-lg hover:bg-accent z-10"
        title={leftPanelOpen ? 'Hide Left Panel' : 'Show Left Panel'}
      >
        <Palette size={16} />
      </button>
      
      <button
        onClick={() => setRightPanelOpen(!rightPanelOpen)}
        className="fixed right-2 top-1/2 transform -translate-y-1/2 p-2 bg-background border border-border rounded-md shadow-lg hover:bg-accent z-10"
        title={rightPanelOpen ? 'Hide Properties' : 'Show Properties'}
      >
        <Settings size={16} />
      </button>
    </div>
  );
}
