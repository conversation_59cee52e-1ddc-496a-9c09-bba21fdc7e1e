import { useCallback, useEffect, useRef, useState } from 'react';
import {
    DesignState,
    DevicePreset,
    ElementProps,
    ElementType,
    ElementUpdate,
    ResponsiveBreakpoint,
    UIElement
} from '../types';

// Default device presets
const DEFAULT_DEVICES: DevicePreset[] = [
  { name: 'Mobile', width: 375, height: 667, scale: 1 },
  { name: 'Tablet', width: 768, height: 1024, scale: 0.8 },
  { name: 'Desktop', width: 1440, height: 900, scale: 0.6 },
  { name: 'Wide', width: 1920, height: 1080, scale: 0.5 }
];

// Initial design state
const INITIAL_STATE: DesignState = {
  elements: [],
  selectedElementId: null,
  hoveredElementId: null,
  clipboard: null,
  history: [],
  historyIndex: -1,
  canvas: {
    zoom: 1,
    pan: { x: 0, y: 0 },
    grid: true,
    rulers: false,
    guides: true,
    breakpoint: 'desktop',
    device: DEFAULT_DEVICES[2] // Desktop
  },
  settings: {
    snapToGrid: true,
    gridSize: 8,
    showElementBounds: true,
    showElementNames: false,
    autoSave: true,
    theme: 'dark'
  }
};

export interface UseUIDesignerResult {
  // State
  state: DesignState;
  selectedElement: UIElement | null;
  hoveredElement: UIElement | null;

  // Element Management
  addElement: (type: ElementType, position?: { x: number; y: number }, props?: ElementProps) => string;
  removeElement: (elementId: string) => void;
  updateElement: (elementId: string, updates: ElementUpdate) => void;
  duplicateElement: (elementId: string) => string | null;
  moveElement: (elementId: string, position: { x: number; y: number }) => void;
  resizeElement: (elementId: string, size: { width: number; height: number }) => void;

  // Selection
  selectElement: (elementId: string | null) => void;
  setHoveredElement: (elementId: string | null) => void;

  // Hierarchy
  moveElementToParent: (elementId: string, parentId: string | null, index?: number) => void;
  getElementChildren: (elementId: string) => UIElement[];
  getElementParent: (elementId: string) => UIElement | null;

  // Clipboard
  copyElement: (elementId: string) => void;
  pasteElement: (position?: { x: number; y: number }) => string | null;
  cutElement: (elementId: string) => void;

  // History
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  addToHistory: (action: string) => void;

  // Canvas
  setZoom: (zoom: number) => void;
  setPan: (pan: { x: number; y: number }) => void;
  setBreakpoint: (breakpoint: ResponsiveBreakpoint) => void;
  setDevice: (device: DevicePreset) => void;
  toggleGrid: () => void;
  toggleRulers: () => void;
  toggleGuides: () => void;

  // Import/Export
  exportDesign: () => UIElement[];
  importDesign: (elements: UIElement[]) => void;
  clearDesign: () => void;

  // Utilities
  getElementById: (elementId: string) => UIElement | null;
  generateElementId: () => string;
  getElementPath: (elementId: string) => UIElement[];
}

export function useUIDesigner(): UseUIDesignerResult {
  const [state, setState] = useState<DesignState>(INITIAL_STATE);
  const historyTimeoutRef = useRef<NodeJS.Timeout>();

  // Generate unique element ID
  const generateElementId = useCallback(() => {
    return `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Find element by ID
  const getElementById = useCallback((elementId: string): UIElement | null => {
    const findElement = (elements: UIElement[]): UIElement | null => {
      for (const element of elements) {
        if (element.id === elementId) return element;
        const found = findElement(element.children);
        if (found) return found;
      }
      return null;
    };
    return findElement(state.elements);
  }, [state.elements]);

  // Get selected and hovered elements
  const selectedElement = state.selectedElementId ? getElementById(state.selectedElementId) : null;
  const hoveredElement = state.hoveredElementId ? getElementById(state.hoveredElementId) : null;

  // Add to history with debouncing
  const addToHistory = useCallback((action: string) => {
    if (historyTimeoutRef.current) {
      clearTimeout(historyTimeoutRef.current);
    }

    historyTimeoutRef.current = setTimeout(() => {
      setState(prev => {
        const newHistory = prev.history.slice(0, prev.historyIndex + 1);
        newHistory.push({
          elements: JSON.parse(JSON.stringify(prev.elements)),
          timestamp: Date.now(),
          action
        });

        // Limit history to 50 entries
        if (newHistory.length > 50) {
          newHistory.shift();
        }

        return {
          ...prev,
          history: newHistory,
          historyIndex: newHistory.length - 1
        };
      });
    }, 300);
  }, []);

  // Add element
  const addElement = useCallback((
    type: ElementType,
    position = { x: 100, y: 100 },
    props: ElementProps = {}
  ): string => {
    const id = generateElementId();
    const newElement: UIElement = {
      id,
      type,
      name: `${type}_${id.split('_')[1]}`,
      props,
      styles: {
        position: 'absolute',
        left: position.x,
        top: position.y,
        width: 100,
        height: 50,
        backgroundColor: type === 'div' ? '#f3f4f6' : undefined,
        border: '1px solid #e5e7eb',
        borderRadius: 4
      },
      children: [],
      position: {
        x: position.x,
        y: position.y,
        width: 100,
        height: 50
      }
    };

    setState(prev => ({
      ...prev,
      elements: [...prev.elements, newElement],
      selectedElementId: id
    }));

    addToHistory(`Add ${type} element`);
    return id;
  }, [generateElementId, addToHistory]);

  // Remove element
  const removeElement = useCallback((elementId: string) => {
    setState(prev => {
      const removeFromElements = (elements: UIElement[]): UIElement[] => {
        return elements
          .filter(el => el.id !== elementId)
          .map(el => ({
            ...el,
            children: removeFromElements(el.children)
          }));
      };

      return {
        ...prev,
        elements: removeFromElements(prev.elements),
        selectedElementId: prev.selectedElementId === elementId ? null : prev.selectedElementId,
        hoveredElementId: prev.hoveredElementId === elementId ? null : prev.hoveredElementId
      };
    });

    addToHistory(`Remove element`);
  }, [addToHistory]);

  // Update element
  const updateElement = useCallback((elementId: string, updates: ElementUpdate) => {
    setState(prev => {
      const updateInElements = (elements: UIElement[]): UIElement[] => {
        return elements.map(el => {
          if (el.id === elementId) {
            return { ...el, ...updates };
          }
          return {
            ...el,
            children: updateInElements(el.children)
          };
        });
      };

      return {
        ...prev,
        elements: updateInElements(prev.elements)
      };
    });

    addToHistory(`Update element`);
  }, [addToHistory]);

  // Duplicate element
  const duplicateElement = useCallback((elementId: string): string | null => {
    const element = getElementById(elementId);
    if (!element) return null;

    const duplicateElementRecursive = (el: UIElement): UIElement => {
      const newId = generateElementId();
      return {
        ...el,
        id: newId,
        name: `${el.name}_copy`,
        position: {
          ...el.position,
          x: el.position.x + 20,
          y: el.position.y + 20
        },
        styles: {
          ...el.styles,
          left: (el.styles.left as number || 0) + 20,
          top: (el.styles.top as number || 0) + 20
        },
        children: el.children.map(duplicateElementRecursive)
      };
    };

    const duplicated = duplicateElementRecursive(element);

    setState(prev => ({
      ...prev,
      elements: [...prev.elements, duplicated],
      selectedElementId: duplicated.id
    }));

    addToHistory(`Duplicate element`);
    return duplicated.id;
  }, [getElementById, generateElementId, addToHistory]);

  // Move element
  const moveElement = useCallback((elementId: string, position: { x: number; y: number }) => {
    updateElement(elementId, {
      position: { ...getElementById(elementId)?.position!, ...position },
      styles: { left: position.x, top: position.y }
    });
  }, [updateElement, getElementById]);

  // Resize element
  const resizeElement = useCallback((elementId: string, size: { width: number; height: number }) => {
    updateElement(elementId, {
      position: { ...getElementById(elementId)?.position!, ...size },
      styles: { width: size.width, height: size.height }
    });
  }, [updateElement, getElementById]);

  // Selection
  const selectElement = useCallback((elementId: string | null) => {
    setState(prev => ({ ...prev, selectedElementId: elementId }));
  }, []);

  const setHoveredElement = useCallback((elementId: string | null) => {
    setState(prev => ({ ...prev, hoveredElementId: elementId }));
  }, []);

  // Clipboard operations
  const copyElement = useCallback((elementId: string) => {
    const element = getElementById(elementId);
    if (element) {
      setState(prev => ({ ...prev, clipboard: element }));
    }
  }, [getElementById]);

  const pasteElement = useCallback((position = { x: 100, y: 100 }): string | null => {
    if (!state.clipboard) return null;

    const duplicateElementRecursive = (el: UIElement): UIElement => {
      const newId = generateElementId();
      return {
        ...el,
        id: newId,
        name: `${el.name}_paste`,
        position: { ...el.position, x: position.x, y: position.y },
        styles: { ...el.styles, left: position.x, top: position.y },
        children: el.children.map(duplicateElementRecursive)
      };
    };

    const pasted = duplicateElementRecursive(state.clipboard);

    setState(prev => ({
      ...prev,
      elements: [...prev.elements, pasted],
      selectedElementId: pasted.id
    }));

    addToHistory(`Paste element`);
    return pasted.id;
  }, [state.clipboard, generateElementId, addToHistory]);

  const cutElement = useCallback((elementId: string) => {
    copyElement(elementId);
    removeElement(elementId);
  }, [copyElement, removeElement]);

  // History operations
  const undo = useCallback(() => {
    if (state.historyIndex > 0) {
      const prevState = state.history[state.historyIndex - 1];
      setState(prev => ({
        ...prev,
        elements: prevState.elements,
        historyIndex: prev.historyIndex - 1,
        selectedElementId: null
      }));
    }
  }, [state.historyIndex, state.history]);

  const redo = useCallback(() => {
    if (state.historyIndex < state.history.length - 1) {
      const nextState = state.history[state.historyIndex + 1];
      setState(prev => ({
        ...prev,
        elements: nextState.elements,
        historyIndex: prev.historyIndex + 1,
        selectedElementId: null
      }));
    }
  }, [state.historyIndex, state.history]);

  const canUndo = state.historyIndex > 0;
  const canRedo = state.historyIndex < state.history.length - 1;

  // Canvas operations
  const setZoom = useCallback((zoom: number) => {
    setState(prev => ({
      ...prev,
      canvas: { ...prev.canvas, zoom: Math.max(0.1, Math.min(5, zoom)) }
    }));
  }, []);

  const setPan = useCallback((pan: { x: number; y: number }) => {
    setState(prev => ({
      ...prev,
      canvas: { ...prev.canvas, pan }
    }));
  }, []);

  const setBreakpoint = useCallback((breakpoint: ResponsiveBreakpoint) => {
    const device = DEFAULT_DEVICES.find(d => d.name.toLowerCase() === breakpoint) || DEFAULT_DEVICES[2];
    setState(prev => ({
      ...prev,
      canvas: { ...prev.canvas, breakpoint, device }
    }));
  }, []);

  const setDevice = useCallback((device: DevicePreset) => {
    setState(prev => ({
      ...prev,
      canvas: { ...prev.canvas, device }
    }));
  }, []);

  const toggleGrid = useCallback(() => {
    setState(prev => ({
      ...prev,
      canvas: { ...prev.canvas, grid: !prev.canvas.grid }
    }));
  }, []);

  const toggleRulers = useCallback(() => {
    setState(prev => ({
      ...prev,
      canvas: { ...prev.canvas, rulers: !prev.canvas.rulers }
    }));
  }, []);

  const toggleGuides = useCallback(() => {
    setState(prev => ({
      ...prev,
      canvas: { ...prev.canvas, guides: !prev.canvas.guides }
    }));
  }, []);

  // Import/Export
  const exportDesign = useCallback(() => {
    return JSON.parse(JSON.stringify(state.elements));
  }, [state.elements]);

  const importDesign = useCallback((elements: UIElement[]) => {
    setState(prev => ({
      ...prev,
      elements: JSON.parse(JSON.stringify(elements)),
      selectedElementId: null,
      hoveredElementId: null
    }));
    addToHistory('Import design');
  }, [addToHistory]);

  const clearDesign = useCallback(() => {
    setState(prev => ({
      ...prev,
      elements: [],
      selectedElementId: null,
      hoveredElementId: null
    }));
    addToHistory('Clear design');
  }, [addToHistory]);

  // Hierarchy helpers
  const moveElementToParent = useCallback((elementId: string, parentId: string | null, index?: number) => {
    setState(prev => {
      // Remove element from current position
      const removeFromElements = (elements: UIElement[]): { elements: UIElement[], removed: UIElement | null } => {
        for (let i = 0; i < elements.length; i++) {
          if (elements[i].id === elementId) {
            const removed = elements[i];
            return {
              elements: [...elements.slice(0, i), ...elements.slice(i + 1)],
              removed
            };
          }
          const result = removeFromElements(elements[i].children);
          if (result.removed) {
            return {
              elements: elements.map((el, idx) =>
                idx === i ? { ...el, children: result.elements } : el
              ),
              removed: result.removed
            };
          }
        }
        return { elements, removed: null };
      };

      const { elements: elementsAfterRemoval, removed } = removeFromElements(prev.elements);
      if (!removed) return prev;

      // Add element to new parent
      if (parentId === null) {
        // Add to root level
        const insertIndex = index !== undefined ? index : elementsAfterRemoval.length;
        const newElements = [...elementsAfterRemoval];
        newElements.splice(insertIndex, 0, { ...removed, parent: undefined });
        return { ...prev, elements: newElements };
      } else {
        // Add to specific parent
        const addToParent = (elements: UIElement[]): UIElement[] => {
          return elements.map(el => {
            if (el.id === parentId) {
              const newChildren = [...el.children];
              const insertIndex = index !== undefined ? index : newChildren.length;
              newChildren.splice(insertIndex, 0, { ...removed, parent: parentId });
              return { ...el, children: newChildren };
            }
            return { ...el, children: addToParent(el.children) };
          });
        };

        return { ...prev, elements: addToParent(elementsAfterRemoval) };
      }
    });

    addToHistory('Move element');
  }, [addToHistory]);

  const getElementChildren = useCallback((elementId: string): UIElement[] => {
    const element = getElementById(elementId);
    return element ? element.children : [];
  }, [getElementById]);

  const getElementParent = useCallback((elementId: string): UIElement | null => {
    const findParent = (elements: UIElement[], targetId: string): UIElement | null => {
      for (const element of elements) {
        if (element.children.some(child => child.id === targetId)) {
          return element;
        }
        const found = findParent(element.children, targetId);
        if (found) return found;
      }
      return null;
    };
    return findParent(state.elements, elementId);
  }, [state.elements]);

  const getElementPath = useCallback((elementId: string): UIElement[] => {
    const path: UIElement[] = [];
    let current = getElementById(elementId);

    while (current) {
      path.unshift(current);
      const parent = getElementParent(current.id);
      current = parent;
    }

    return path;
  }, [getElementById, getElementParent]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (historyTimeoutRef.current) {
        clearTimeout(historyTimeoutRef.current);
      }
    };
  }, []);

  return {
    state,
    selectedElement,
    hoveredElement,
    addElement,
    removeElement,
    updateElement,
    duplicateElement,
    moveElement,
    resizeElement,
    selectElement,
    setHoveredElement,
    moveElementToParent,
    getElementChildren,
    getElementParent,
    copyElement,
    pasteElement,
    cutElement,
    undo,
    redo,
    canUndo,
    canRedo,
    addToHistory,
    setZoom,
    setPan,
    setBreakpoint,
    setDevice,
    toggleGrid,
    toggleRulers,
    toggleGuides,
    exportDesign,
    importDesign,
    clearDesign,
    getElementById,
    generateElementId,
    getElementPath
  };
}
