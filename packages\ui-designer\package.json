{"name": "@kodekilat/ui-designer", "version": "1.0.0", "description": "Visual UI Designer for KodeKilat Studio - drag & drop interface builder with live preview", "main": "index.ts", "types": "index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "keywords": ["ui-designer", "visual-builder", "drag-drop", "react", "typescript", "kode<PERSON>lat"], "author": "KodeKilat Studio", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.4", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "typescript": "^5.2.2"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}