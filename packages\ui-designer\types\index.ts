import { ReactNode } from 'react';

// Core UI Element Types
export interface UIElement {
  id: string;
  type: ElementType;
  name: string;
  props: ElementProps;
  styles: StyleProperties;
  children: UIElement[];
  parent?: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  responsive?: ResponsiveStyles;
  locked?: boolean;
  hidden?: boolean;
}

export type ElementType = 
  // HTML Elements
  | 'div' | 'span' | 'p' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  | 'button' | 'input' | 'textarea' | 'select' | 'label' | 'form'
  | 'img' | 'video' | 'audio' | 'iframe' | 'canvas'
  | 'ul' | 'ol' | 'li' | 'table' | 'tr' | 'td' | 'th'
  | 'header' | 'nav' | 'main' | 'section' | 'article' | 'aside' | 'footer'
  // React Components
  | 'Card' | 'Button' | 'Input' | 'Select' | 'Checkbox' | 'Radio'
  | 'Modal' | 'Dialog' | 'Popover' | 'Tooltip' | 'Badge' | 'Avatar'
  | 'Tabs' | 'Accordion' | 'Slider' | 'Progress' | 'Alert'
  // Layout Components
  | 'Container' | 'Grid' | 'Flex' | 'Stack' | 'Spacer'
  // Custom Components
  | 'Component';

export interface ElementProps {
  [key: string]: PropertyValue;
}

export type PropertyValue = string | number | boolean | object | null | undefined;

export interface StyleProperties {
  // Layout
  display?: string;
  position?: string;
  top?: string | number;
  right?: string | number;
  bottom?: string | number;
  left?: string | number;
  width?: string | number;
  height?: string | number;
  minWidth?: string | number;
  maxWidth?: string | number;
  minHeight?: string | number;
  maxHeight?: string | number;
  
  // Flexbox
  flexDirection?: string;
  justifyContent?: string;
  alignItems?: string;
  alignContent?: string;
  flexWrap?: string;
  flex?: string;
  flexGrow?: number;
  flexShrink?: number;
  flexBasis?: string | number;
  
  // Grid
  gridTemplateColumns?: string;
  gridTemplateRows?: string;
  gridColumn?: string;
  gridRow?: string;
  gap?: string | number;
  
  // Spacing
  margin?: string | number;
  marginTop?: string | number;
  marginRight?: string | number;
  marginBottom?: string | number;
  marginLeft?: string | number;
  padding?: string | number;
  paddingTop?: string | number;
  paddingRight?: string | number;
  paddingBottom?: string | number;
  paddingLeft?: string | number;
  
  // Typography
  fontSize?: string | number;
  fontWeight?: string | number;
  fontFamily?: string;
  lineHeight?: string | number;
  textAlign?: string;
  textDecoration?: string;
  textTransform?: string;
  color?: string;
  
  // Background
  backgroundColor?: string;
  backgroundImage?: string;
  backgroundSize?: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;
  
  // Border
  border?: string;
  borderTop?: string;
  borderRight?: string;
  borderBottom?: string;
  borderLeft?: string;
  borderRadius?: string | number;
  borderWidth?: string | number;
  borderStyle?: string;
  borderColor?: string;
  
  // Effects
  boxShadow?: string;
  opacity?: number;
  transform?: string;
  transition?: string;
  filter?: string;
  
  // Overflow
  overflow?: string;
  overflowX?: string;
  overflowY?: string;
  
  // Z-index
  zIndex?: number;
  
  // Custom CSS
  [key: string]: PropertyValue;
}

export interface ResponsiveStyles {
  mobile?: StyleProperties;
  tablet?: StyleProperties;
  desktop?: StyleProperties;
  wide?: StyleProperties;
}

export type ResponsiveBreakpoint = 'mobile' | 'tablet' | 'desktop' | 'wide';

// Design State
export interface DesignState {
  elements: UIElement[];
  selectedElementId: string | null;
  hoveredElementId: string | null;
  clipboard: UIElement | null;
  history: HistoryState[];
  historyIndex: number;
  canvas: CanvasState;
  settings: DesignSettings;
}

export interface HistoryState {
  elements: UIElement[];
  timestamp: number;
  action: string;
}

export interface CanvasState {
  zoom: number;
  pan: { x: number; y: number };
  grid: boolean;
  rulers: boolean;
  guides: boolean;
  breakpoint: ResponsiveBreakpoint;
  device: DevicePreset;
}

export interface DevicePreset {
  name: string;
  width: number;
  height: number;
  scale: number;
}

export interface DesignSettings {
  snapToGrid: boolean;
  gridSize: number;
  showElementBounds: boolean;
  showElementNames: boolean;
  autoSave: boolean;
  theme: 'light' | 'dark';
}

// Drag & Drop
export interface DragDropState {
  isDragging: boolean;
  draggedElement: UIElement | ElementType | null;
  draggedFromPalette: boolean;
  dropTarget: string | null;
  dropPosition: DropPosition;
  dragOffset: { x: number; y: number };
}

export type DropPosition = 'before' | 'after' | 'inside' | 'none';

// Component Library
export interface ComponentLibrary {
  categories: ComponentCategory[];
}

export interface ComponentCategory {
  id: string;
  name: string;
  icon: ReactNode;
  elements: ElementDefinition[];
}

export interface ElementDefinition {
  type: ElementType;
  name: string;
  description: string;
  icon: ReactNode;
  defaultProps: ElementProps;
  defaultStyles: StyleProperties;
  category: string;
  tags: string[];
  preview?: ReactNode;
}

// Code Generation
export interface GeneratedCode {
  jsx: string;
  css: string;
  typescript?: string;
  imports: string[];
  dependencies: string[];
}

export interface CodeGenerationOptions {
  framework: 'react' | 'next' | 'vue' | 'svelte';
  styling: 'css' | 'tailwind' | 'styled-components' | 'emotion';
  typescript: boolean;
  prettier: boolean;
  componentName: string;
  exportType: 'default' | 'named';
}

// Property Panel
export interface PropertyGroup {
  id: string;
  name: string;
  icon: ReactNode;
  properties: PropertyDefinition[];
  collapsed?: boolean;
}

export interface PropertyDefinition {
  key: string;
  label: string;
  type: PropertyType;
  defaultValue?: PropertyValue;
  options?: PropertyOption[];
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  description?: string;
  conditional?: PropertyCondition;
}

export type PropertyType = 
  | 'text' | 'number' | 'boolean' | 'select' | 'color' | 'image'
  | 'spacing' | 'border' | 'shadow' | 'gradient' | 'animation'
  | 'responsive' | 'code' | 'json';

export interface PropertyOption {
  label: string;
  value: PropertyValue;
  icon?: ReactNode;
}

export interface PropertyCondition {
  property: string;
  operator: 'equals' | 'not-equals' | 'contains' | 'not-contains';
  value: PropertyValue;
}

// Canvas Refs
export interface DesignCanvasRef {
  addElement: (element: UIElement, position?: { x: number; y: number }) => void;
  removeElement: (elementId: string) => void;
  selectElement: (elementId: string | null) => void;
  updateElement: (elementId: string, updates: Partial<UIElement>) => void;
  duplicateElement: (elementId: string) => void;
  moveElement: (elementId: string, position: { x: number; y: number }) => void;
  resizeElement: (elementId: string, size: { width: number; height: number }) => void;
  undo: () => void;
  redo: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  fitToScreen: () => void;
  exportDesign: () => UIElement[];
  importDesign: (elements: UIElement[]) => void;
}

// Events
export interface ElementEvent {
  type: 'select' | 'hover' | 'unhover' | 'update' | 'delete' | 'duplicate' | 'move' | 'resize';
  elementId: string;
  element?: UIElement;
  data?: any;
}

export interface CanvasEvent {
  type: 'zoom' | 'pan' | 'resize' | 'breakpoint-change' | 'device-change';
  data: any;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type ElementUpdate = DeepPartial<Omit<UIElement, 'id' | 'children'>>;

export type StyleUpdate = DeepPartial<StyleProperties>;
