{"$schema": "http://json-schema.org/draft-07/schema#", "title": "KODIX Extension Manifest", "description": "Schema for KodeKilat Studio KODIX extension manifest files", "type": "object", "required": ["name", "version", "description", "main", "author"], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9-]+$", "minLength": 1, "maxLength": 50, "description": "Extension name (lowercase, alphanumeric, hyphens only)"}, "displayName": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable extension name"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9-]+)?$", "description": "Extension version (semver format)"}, "description": {"type": "string", "minLength": 10, "maxLength": 500, "description": "Extension description"}, "main": {"type": "string", "pattern": "\\.(tsx?|jsx?)$", "description": "Main entry point file"}, "author": {"oneOf": [{"type": "string", "description": "Author name"}, {"type": "object", "properties": {"name": {"type": "string", "description": "Author name"}, "email": {"type": "string", "format": "email", "description": "Author email"}, "url": {"type": "string", "format": "uri", "description": "Author website"}}, "required": ["name"]}]}, "license": {"type": "string", "description": "License identifier (SPDX format)"}, "homepage": {"type": "string", "format": "uri", "description": "Extension homepage URL"}, "repository": {"type": "object", "properties": {"type": {"type": "string", "enum": ["git", "svn", "hg"], "description": "Repository type"}, "url": {"type": "string", "format": "uri", "description": "Repository URL"}}, "required": ["type", "url"]}, "bugs": {"oneOf": [{"type": "string", "format": "uri", "description": "Bug tracker URL"}, {"type": "object", "properties": {"url": {"type": "string", "format": "uri", "description": "Bug tracker URL"}, "email": {"type": "string", "format": "email", "description": "Bug report email"}}}]}, "keywords": {"type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 30}, "maxItems": 10, "description": "Extension keywords for search"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["AI", "Debuggers", "Extension Packs", "Formatters", "Keymaps", "Language Packs", "Linters", "Machine Learning", "Notebooks", "Other", "Programming Languages", "SCM Providers", "Snippets", "Testing", "Themes", "Visualization"]}, "maxItems": 3, "description": "Extension categories"}, "activationEvents": {"type": "array", "items": {"type": "string", "pattern": "^(onLanguage:|onCommand:|onDebug:|onFileSystem:|onStartupFinished|\\*)$"}, "description": "Events that activate the extension"}, "contributes": {"type": "object", "properties": {"commands": {"type": "array", "items": {"type": "object", "properties": {"command": {"type": "string", "description": "Command identifier"}, "title": {"type": "string", "description": "Command title"}, "category": {"type": "string", "description": "Command category"}, "icon": {"type": "string", "description": "Command icon"}}, "required": ["command", "title"]}}, "keybindings": {"type": "array", "items": {"type": "object", "properties": {"command": {"type": "string", "description": "Command to bind"}, "key": {"type": "string", "description": "Key combination"}, "when": {"type": "string", "description": "When clause"}}, "required": ["command", "key"]}}, "languages": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Language identifier"}, "aliases": {"type": "array", "items": {"type": "string"}, "description": "Language aliases"}, "extensions": {"type": "array", "items": {"type": "string"}, "description": "File extensions"}}, "required": ["id"]}}, "themes": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "Theme label"}, "uiTheme": {"type": "string", "enum": ["vs", "vs-dark", "hc-black"], "description": "Base UI theme"}, "path": {"type": "string", "description": "Theme file path"}}, "required": ["label", "uiTheme", "path"]}}}}, "engines": {"type": "object", "properties": {"kodekilat": {"type": "string", "pattern": "^\\^?\\d+\\.\\d+\\.\\d+", "description": "Minimum KodeKilat Studio version"}}, "required": ["kode<PERSON>lat"]}, "dependencies": {"type": "object", "patternProperties": {"^[a-zA-Z0-9@/_-]+$": {"type": "string"}}, "description": "Extension dependencies"}, "extensionDependencies": {"type": "array", "items": {"type": "string", "pattern": "^[a-z0-9-]+$"}, "description": "Other KODIX extensions this extension depends on"}, "icon": {"type": "string", "pattern": "\\.(png|jpg|jpeg|svg)$", "description": "Extension icon file"}, "galleryBanner": {"type": "object", "properties": {"color": {"type": "string", "pattern": "^#[0-9a-fA-F]{6}$", "description": "Banner background color"}, "theme": {"type": "string", "enum": ["dark", "light"], "description": "Banner theme"}}}}}