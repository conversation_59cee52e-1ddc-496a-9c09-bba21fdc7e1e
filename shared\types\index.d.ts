// KodeKilat Studio - Shared Type Definitions

// File System Types
export interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modified?: string;
  created?: string;
  isHidden?: boolean;
  extension?: string;
  language?: string;
}

export interface DirectoryTree {
  name: string;
  path: string;
  type: 'directory';
  children: (FileItem | DirectoryTree)[];
  expanded?: boolean;
}

export interface FileContent {
  path: string;
  content: string;
  encoding?: string;
  language?: string;
  modified?: boolean;
}

// Editor Types
export interface EditorTab {
  id: string;
  path: string;
  name: string;
  content: string;
  language: string;
  modified: boolean;
  active: boolean;
  pinned?: boolean;
}

export interface EditorPosition {
  line: number;
  column: number;
}

export interface EditorSelection {
  start: EditorPosition;
  end: EditorPosition;
}

export interface EditorChange {
  range: {
    start: EditorPosition;
    end: EditorPosition;
  };
  text: string;
  timestamp: string;
}

// AI Types
export interface AIMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  context?: {
    filePath?: string;
    selectedText?: string;
    language?: string;
  };
}

export interface AIProvider {
  id: string;
  name: string;
  type: 'openai' | 'anthropic' | 'local' | 'huggingface';
  apiKey?: string;
  baseUrl?: string;
  model: string;
  enabled: boolean;
}

export interface AIRequest {
  message: string;
  context?: {
    filePath?: string;
    selectedText?: string;
    language?: string;
    projectContext?: string;
  };
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface AIResponse {
  id: string;
  content: string;
  provider: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  timestamp: string;
}

// Extension Types
export interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  type: 'vsix' | 'kodix';
  enabled: boolean;
  path: string;
  manifest: ExtensionManifest;
  installedAt: string;
}

export interface ExtensionManifest {
  name: string;
  version: string;
  description: string;
  author: string;
  main?: string;
  activationEvents?: string[];
  contributes?: {
    commands?: ExtensionCommand[];
    keybindings?: ExtensionKeybinding[];
    languages?: ExtensionLanguage[];
    themes?: ExtensionTheme[];
    snippets?: ExtensionSnippet[];
  };
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export interface ExtensionCommand {
  command: string;
  title: string;
  category?: string;
  icon?: string;
}

export interface ExtensionKeybinding {
  command: string;
  key: string;
  when?: string;
}

export interface ExtensionLanguage {
  id: string;
  aliases: string[];
  extensions: string[];
  configuration?: string;
}

export interface ExtensionTheme {
  label: string;
  uiTheme: 'vs' | 'vs-dark' | 'hc-black';
  path: string;
}

export interface ExtensionSnippet {
  language: string;
  path: string;
}

// Terminal Types
export interface Terminal {
  id: string;
  name: string;
  cwd: string;
  shell: string;
  active: boolean;
  pid?: number;
  createdAt: string;
}

export interface TerminalCommand {
  id: string;
  command: string;
  output: string;
  exitCode?: number;
  timestamp: string;
  duration?: number;
}

// Collaboration Types
export interface CollaborationSession {
  id: string;
  name: string;
  projectPath: string;
  ownerId: string;
  isPublic: boolean;
  participants: SessionParticipant[];
  createdAt: string;
  updatedAt: string;
}

export interface SessionParticipant {
  id: string;
  name: string;
  role: 'owner' | 'collaborator' | 'viewer';
  joinedAt: string;
  lastActivity: string;
  isOnline: boolean;
  permissions: {
    read: boolean;
    write: boolean;
    admin: boolean;
  };
}

export interface CursorPosition {
  userId: string;
  filePath: string;
  position: EditorPosition;
  selection?: EditorSelection;
  timestamp: string;
}

export interface TextChange {
  userId: string;
  filePath: string;
  changes: EditorChange[];
  version: number;
  timestamp: string;
}

// Settings Types
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  fontSize: number;
  fontFamily: string;
  tabSize: number;
  wordWrap: boolean;
  minimap: boolean;
  lineNumbers: boolean;
  autoSave: boolean;
  formatOnSave: boolean;
  aiProvider: string;
  language: 'en' | 'id';
  keybindings: Record<string, string>;
  extensions: {
    autoUpdate: boolean;
    enabledExtensions: string[];
  };
}

export interface WorkspaceSettings {
  workspacePath: string;
  name: string;
  description?: string;
  settings: {
    files: {
      exclude: string[];
      watcherExclude: string[];
    };
    search: {
      exclude: string[];
    };
    editor: Partial<UserSettings>;
  };
  folders: string[];
  extensions: {
    recommendations: string[];
  };
}

// Project Types
export interface Project {
  id: string;
  name: string;
  path: string;
  type: 'javascript' | 'typescript' | 'python' | 'java' | 'cpp' | 'rust' | 'go' | 'php' | 'other';
  framework?: string;
  lastOpened: string;
  createdAt: string;
  gitRepository?: {
    url: string;
    branch: string;
    status: 'clean' | 'modified' | 'staged' | 'conflict';
  };
  packageManager?: 'npm' | 'yarn' | 'pnpm' | 'bun' | 'pip' | 'cargo' | 'go mod' | 'composer';
}

// Preview Types
export interface PreviewServer {
  id: string;
  projectPath: string;
  port: number;
  url: string;
  framework: string;
  status: 'starting' | 'running' | 'stopped' | 'error';
  pid?: number;
  logs: PreviewLog[];
  createdAt: string;
}

export interface PreviewLog {
  id: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: string;
}

// Git Types
export interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  staged: GitFileStatus[];
  unstaged: GitFileStatus[];
  untracked: string[];
  conflicted: string[];
}

export interface GitFileStatus {
  path: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied';
  staged: boolean;
}

export interface GitCommit {
  hash: string;
  message: string;
  author: string;
  date: string;
  files: string[];
}

// Search Types
export interface SearchResult {
  file: string;
  line: number;
  column: number;
  text: string;
  match: string;
  context: {
    before: string[];
    after: string[];
  };
}

export interface SearchOptions {
  query: string;
  caseSensitive: boolean;
  wholeWord: boolean;
  regex: boolean;
  includeFiles: string[];
  excludeFiles: string[];
  maxResults: number;
}

// API Response Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T = any> extends APIResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  userId?: string;
  sessionId?: string;
}

// Electron API Types
export interface ElectronAPI {
  openFile: () => Promise<any>;
  openFolder: () => Promise<any>;
  saveFile: () => Promise<any>;
  store: {
    get: (key: string) => Promise<any>;
    set: (key: string, value: any) => Promise<void>;
  };
  window: {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
  };
  onMenuAction: (callback: (channel: string, ...args: any[]) => void) => () => void;
  platform: string;
  versions: {
    node: string;
    chrome: string;
    electron: string;
  };
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
