{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/apps/*": ["./apps/*"], "@/packages/*": ["./packages/*"], "@/shared/*": ["./shared/*"], "@/studio/*": ["./apps/studio/*"], "@/ui/*": ["./apps/studio/ui/*"], "@/api/*": ["./apps/api-server/*"]}, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", "build", ".next"]}