{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@kodekilat/*": ["packages/*"], "@/*": ["*"]}}, "include": ["apps/**/*", "packages/**/*", "shared/**/*"], "exclude": ["node_modules", "**/node_modules", "**/dist", "**/build", "**/.next"], "references": [{"path": "./apps/studio"}, {"path": "./apps/api-server"}, {"path": "./packages/ui-designer"}]}